import numpy as np
import os
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn import TransformerEncoder, TransformerEncoderLayer
import math
from typing import Dict, Tuple, Optional, Union

# 添加调试信息
print("🔍 modelgai.py 正在加载...")
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")

# ============================
# 核心创新模块
# ============================

# Removed TruePixelCNN1D - unused complex implementation

# Removed AdvancedTransferAutoEncoder - overly complex for the task

class EnhancedLandingGearPhysicsModule(nn.Module):
    """增强的起落架动力学物理约束模块 - 基于完整的RK_newmodel.m实现"""
    def __init__(self, hidden_dim=256, use_full_rk=True):
        super(EnhancedLandingGearPhysicsModule, self).__init__()

        self.use_full_rk = use_full_rk

        # 起落架结构参数 (完全基于fun_parameter.m)
        # 主要压缩参数
        self.Aa = nn.Parameter(torch.tensor(1385e-6))      # 主腔压缩面积
        self.Ahl = nn.Parameter(torch.tensor(2246e-6))     # 主腔液压面积
        self.Aal = nn.Parameter(torch.tensor(2122e-6))     # 副腔压缩面积
        self.Aah = nn.Parameter(torch.tensor(2122e-6))     # 副腔液压面积

        # 压力和体积参数
        self.Pa0 = nn.Parameter(torch.tensor(1.65e6))      # 初始气压
        self.Ph0 = nn.Parameter(torch.tensor(1.65e6))     # 初始液压
        self.Va0 = nn.Parameter(torch.tensor(461e-6))      # 初始气体体积
        self.Vh0 = nn.Parameter(torch.tensor(461e-6))     # 初始液体体积
        self.n = nn.Parameter(torch.tensor(1.0))           # 多变过程指数
        self.Patm = nn.Parameter(torch.tensor(1.014e5))    # 大气压力

        # 液压阻尼参数 (完整版本)
        self.r = nn.Parameter(torch.tensor(861.0))         # 油液密度
        self.Cd1 = nn.Parameter(torch.tensor(0.8))         # 压缩流量系数
        self.Cd2 = nn.Parameter(torch.tensor(0.8))         # 回弹流量系数
        self.Cds = nn.Parameter(torch.tensor(0.8))         # 副腔流量系数
        self.Adl1 = nn.Parameter(torch.tensor(180e-6))     # 压缩孔面积
        self.Adl2 = nn.Parameter(torch.tensor(70e-6))      # 回弹孔面积

        # 摩擦和结构参数
        self.Km = nn.Parameter(torch.tensor(0.1))          # 密封摩擦系数
        self.mu_b = nn.Parameter(torch.tensor(0.05))       # 支柱摩擦系数
        self.mu_f0 = nn.Parameter(torch.tensor(0.55))      # 最大滑动摩擦系数

        # 结构几何参数
        self.K = nn.Parameter(torch.tensor(3e3))           # 轮胎支撑刚度系数
        self.M_1 = nn.Parameter(torch.tensor(661.0))       # 落锤质量
        self.M_2 = nn.Parameter(torch.tensor(24.0))        # 机轮质量
        self.L_b = nn.Parameter(torch.tensor(0.48))        # 支柱长度
        self.L_c = nn.Parameter(torch.tensor(0.185))       # 重心到支点距离
        self.Rt = nn.Parameter(torch.tensor(0.19))         # 轮胎半径
        self.Iu = nn.Parameter(torch.tensor(0.149815))     # 轮胎转动惯量
        self.g = nn.Parameter(torch.tensor(10.0))          # 重力加速度

        # 轮胎特性多项式系数 (基于fun_parameter.m中的拟合)
        # 轮胎垂直特性: Vtt vs st
        self.register_buffer('tire_poly_coeff', torch.tensor([
            3.33e5, 0.0  # 简化的线性拟合系数，实际应该用polyfit结果
        ]))

        # 初始速度多项式系数: vt vs hh
        self.register_buffer('velocity_poly_coeff', torch.tensor([
            0.3, 1.0  # 简化的线性拟合系数
        ]))

        # 孔面积多项式系数: Ahh vs ss (实际为常数)
        self.register_buffer('orifice_poly_coeff', torch.tensor([
            0.0, 21.226e-6  # 常数项
        ]))

        # 神经网络修正模块 (可选的，用于微调物理模型)
        self.nn_correction = nn.Sequential(
            nn.Linear(2, hidden_dim//2),  # 输入: height, mass
            nn.LayerNorm(hidden_dim//2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim//2, hidden_dim//4),
            nn.GELU(),
            nn.Linear(hidden_dim//4, 3)  # 输出: [Pz修正, S修正, Yc修正]
        )

        # RK4积分参数
        self.dt = 0.001  # 时间步长
        self.t_max = 3.0  # 最大仿真时间

    def polyval(self, coeffs, x):
        """多项式求值函数，模拟MATLAB的polyval"""
        result = torch.zeros_like(x)
        for i, coeff in enumerate(coeffs):
            result += coeff * torch.pow(x, len(coeffs) - 1 - i)
        return result

    def rk4_step(self, state, t, dt, height, mass):
        """
        RK4积分步骤，基于RK_newmodel.m的完整实现
        state: [x1, x2, x3, x4, x5, x6, x7, x8, x9, x10] - 10个状态变量
        """
        def dynamics(state, t):
            x1, x2, x3, x4, x5, x6, x7, x8, x9, x10 = state

            # 计算孔面积 (基于MATLAB中的polyval(para.p5, y5))
            Ah = self.polyval(self.orifice_poly_coeff, x5)

            # 气液压力计算 (完全按照MATLAB实现)
            Fa = self.Aal * (self.Pa0 / torch.pow(1 - self.Aal * x5 / self.Va0 + 1e-8, self.n) - self.Patm)

            # 液压阻尼力计算 (完整的双向流动模型)
            # 压缩阶段 (x6 < 0)
            Fh_compression = (-self.r * torch.pow(self.Aa, 3) / (2 * torch.pow(self.Cd1, 2) * torch.pow(Ah, 2)) * torch.pow(x6, 2) -
                             self.r * torch.pow(self.Ahl, 3) / (2 * torch.pow(self.Cds, 2) * torch.pow(self.Adl2, 2)) * torch.pow(x6, 2))

            # 回弹阶段 (x6 > 0)
            Fh_rebound = (self.r * torch.pow(self.Aa, 3) / (2 * torch.pow(self.Cd2, 2) * torch.pow(Ah, 2)) * torch.pow(x6, 2) +
                         self.r * torch.pow(self.Ahl, 3) / (2 * torch.pow(self.Cds, 2) * torch.pow(self.Adl1, 2)) * torch.pow(x6, 2))

            # 静止状态 (x6 = 0)
            Fh_zero = torch.zeros_like(x6)

            # 根据速度选择阻尼力
            Fh = torch.where(x6 < 0, Fh_compression,
                            torch.where(x6 > 0, Fh_rebound, Fh_zero))

            # 支撑力计算
            N_s = self.K * x7 + 0.7 * torch.sqrt(self.M_2 * self.K) * x8
            N_u = (self.L_b - self.L_c - x5) / (self.L_c + x5 + 1e-8) * N_s
            N_l = self.L_b / (self.L_c + x5 + 1e-8) * N_s

            # 摩擦力计算
            Ff = self.Km * Fa * torch.sign(x6) + self.mu_b * torch.abs(N_u + N_l) * torch.sign(x6)

            # 总支柱力
            Fs = torch.where(x5 <= 0, torch.zeros_like(x5), Fa + Fh + Ff)

            # 轮胎接触计算
            delt = (torch.abs(x1) + x1) / 2.0
            e = 39.2 - (self.Rt - delt / 3) * x10 - x8
            sg = torch.abs(e / 39.2)

            # 摩擦系数计算 (使用torch.where处理多重条件)
            mu_f_1 = 5.62 * sg * self.mu_f0  # sg < 0.2
            mu_f_2 = 5.62 * 0.2 * self.mu_f0  # 0.2 <= sg < 0.25
            mu_f_3 = (-0.62 * sg + 1.279) * self.mu_f0  # 0.25 <= sg < 0.45
            mu_f_4 = self.mu_f0  # sg >= 0.45

            mu_f = torch.where(sg < 0.2, mu_f_1,
                              torch.where(sg < 0.25, mu_f_2,
                                         torch.where(sg < 0.45, mu_f_3, mu_f_4)))

            # 轮胎垂直力
            CT = 0.04
            Vt = (1 + CT * x2) * self.polyval(self.tire_poly_coeff, delt)

            # 状态导数计算 (完全按照MATLAB的k11-k10模式)
            dx1 = x2  # 轮胎压缩速度
            dx2 = (Fs - Vt) / self.M_2 + self.g  # 轮胎压缩加速度
            dx3 = x4  # 落锤速度
            dx4 = -Fs / self.M_1 + self.g  # 落锤加速度
            dx5 = x4 - x2  # 支柱行程速度
            dx6 = dx4 - dx2  # 支柱行程加速度

            # 横向运动计算
            dx7 = x8  # 横向位移速度
            dx8 = mu_f * (Vt / self.M_2) + (N_u - N_l) / self.M_2  # 横向加速度
            dx9 = x10  # 角速度

            # 角加速度计算 (根据接触状态)
            dx10_contact = mu_f * (Vt / self.Iu) * (self.Rt - delt)  # 接触状态
            dx10_no_contact = torch.zeros_like(x10)  # 非接触状态
            dx10 = torch.where(e > 0, dx10_contact, dx10_no_contact)

            return torch.stack([dx1, dx2, dx3, dx4, dx5, dx6, dx7, dx8, dx9, dx10])

        # RK4积分
        k1 = dynamics(state, t)
        k2 = dynamics(state + dt/2 * k1, t + dt/2)
        k3 = dynamics(state + dt/2 * k2, t + dt/2)
        k4 = dynamics(state + dt * k3, t + dt)

        new_state = state + dt/6 * (k1 + 2*k2 + 2*k3 + k4)
        return new_state

    def solve_full_dynamics(self, height, mass):
        """
        完整的动力学求解，基于RK_newmodel.m
        返回时间序列的S, Pz, Yc
        """
        batch_size = height.shape[0] if height.dim() > 0 else 1
        device = height.device

        # 初始化状态 (基于MATLAB的初始值设置)
        initial_velocity = self.polyval(self.velocity_poly_coeff, height / 1000.0)  # 基于高度的初始速度

        # 初始状态: [x1, x2, x3, x4, x5, x6, x7, x8, x9, x10]
        state = torch.zeros(batch_size, 10, device=device)
        state[:, 1] = initial_velocity  # x2: 初始轮胎压缩速度
        state[:, 3] = initial_velocity  # x4: 初始落锤速度
        state[:, 7] = 3.92  # x8: 初始横向速度

        # 时间序列
        t = 0.0
        time_steps = int(self.t_max / self.dt)

        # 存储结果
        S_history = []
        Pz_history = []
        Yc_history = []

        # RK4积分循环
        for i in range(time_steps):
            # 计算当前时刻的输出
            x1, x2, x3, x4, x5, x6, x7, x8, x9, x10 = [state[:, j] for j in range(10)]

            # 计算输出量 (基于MATLAB的输出计算)
            S_current = x5 * 1000  # 支柱行程，转换为mm
            Pz_current = 3.09e5 * x1 / 1000.0  # 垂直载荷，转换为kN
            Yc_current = (x5 + x1) * 1000  # 吊篮位移，转换为mm

            S_history.append(S_current)
            Pz_history.append(Pz_current)
            Yc_history.append(Yc_current)

            # RK4步进
            state = self.rk4_step(state, t, self.dt, height, mass)
            t += self.dt

        # 返回最终值 (或者可以返回整个时间序列)
        S_final = S_history[-1]
        Pz_final = Pz_history[-1]
        Yc_final = Yc_history[-1]

        return Pz_final, S_final, Yc_final

    def forward(self, height, mass, S=None, dS_dt=None, use_full_physics=None):
        """
        增强的物理约束计算
        Args:
            height: 高度 (mm)
            mass: 质量 (kg)
            S: 支柱行程 (mm, 可选)
            dS_dt: 支柱行程速度 (mm/s, 可选)
            use_full_physics: 是否使用完整物理模型 (None=自动选择)
        Returns:
            (Pz_physics, S_physics, Yc_physics)
        """
        # 确保输入为tensor
        if not isinstance(height, torch.Tensor):
            height = torch.tensor(height, dtype=torch.float32)
        if not isinstance(mass, torch.Tensor):
            mass = torch.tensor(mass, dtype=torch.float32)

        # 自动选择物理模型复杂度
        if use_full_physics is None:
            use_full_physics = self.use_full_rk and self.training

        if use_full_physics:
            # 使用完整的RK4求解器
            try:
                Pz_physics, S_physics, Yc_physics = self.solve_full_dynamics(height, mass)
            except:
                # 如果完整求解失败，回退到简化模型
                use_full_physics = False

        if not use_full_physics:
            # 使用简化的物理模型 (改进版本)
            if S is None or dS_dt is None:
                # 基于高度和质量的改进估算
                g = self.g
                impact_velocity = torch.sqrt(2 * g * height / 1000.0)  # m/s

                # 改进的位移估算 (基于实际数据分布)
                height_factor = torch.clamp(height / 3000.0, 0.1, 1.0)  # 归一化高度
                mass_factor = torch.clamp(mass / 3000.0, 0.1, 1.0)    # 归一化质量

                # 更准确的S估算
                S_max = height_factor * mass_factor * 50.0  # mm
                S = torch.clamp(S_max, min=-50, max=40)
                dS_dt = -impact_velocity * 100  # mm/s

            # 单位转换
            S_m = S / 1000.0  # mm to m
            dS_dt_ms = dS_dt / 1000.0  # mm/s to m/s

            # 1. 改进的气液压力计算
            compression_ratio = torch.clamp(self.Aal * torch.abs(S_m) / self.Va0, min=0, max=0.9)
            pressure_ratio = torch.pow(1 - compression_ratio + 1e-6, -self.n)
            Fa = self.Aal * (self.Pa0 * pressure_ratio - self.Patm)
            Fa = torch.clamp(Fa, min=0, max=50000)

            # 2. 改进的液压阻尼力计算
            velocity_factor = torch.abs(dS_dt_ms) + 1e-6
            # 使用更准确的孔面积
            Ah = self.polyval(self.orifice_poly_coeff, torch.abs(S_m))

            # 使用torch.where处理张量条件判断
            # 压缩阶段 (dS_dt_ms < 0)
            Fh_compression = (self.r * torch.pow(self.Aa, 3) / (2 * torch.pow(self.Cd1, 2) * torch.pow(Ah, 2)) * torch.pow(velocity_factor, 2) +
                             self.r * torch.pow(self.Ahl, 3) / (2 * torch.pow(self.Cds, 2) * torch.pow(self.Adl2, 2)) * torch.pow(velocity_factor, 2))

            # 回弹阶段 (dS_dt_ms >= 0)
            Fh_rebound = (self.r * torch.pow(self.Aa, 3) / (2 * torch.pow(self.Cd2, 2) * torch.pow(Ah, 2)) * torch.pow(velocity_factor, 2) +
                         self.r * torch.pow(self.Ahl, 3) / (2 * torch.pow(self.Cds, 2) * torch.pow(self.Adl1, 2)) * torch.pow(velocity_factor, 2))

            # 根据速度方向选择阻尼力
            Fh = torch.where(dS_dt_ms < 0, Fh_compression, Fh_rebound)

            Fh = torch.clamp(Fh, min=0, max=20000)

            # 3. 改进的摩擦力计算
            Ff = self.Km * Fa * torch.sign(dS_dt_ms) + self.mu_b * Fa * 0.5  # 简化的支撑力

            # 4. 改进的轮胎压缩估算
            tire_compression = torch.abs(S_m) * 0.15  # 更合理的比例

            # 5. 垂直载荷计算
            Pz_physics = 3.09e5 * tire_compression / 1000.0  # kN
            Pz_physics = torch.clamp(Pz_physics, min=-2, max=250)

            # 6. 吊篮位移计算
            Yc_physics = torch.abs(S) + tire_compression * 1000  # mm
            Yc_physics = torch.clamp(Yc_physics, min=0, max=100)

            S_physics = S

        # 7. 神经网络微调 (可选)
        if hasattr(self, 'nn_correction'):
            features = torch.stack([
                height / 3000.0,  # 归一化高度
                mass / 3000.0,    # 归一化质量
            ], dim=-1)

            corrections = self.nn_correction(features)
            corrections = torch.clamp(corrections, min=-0.05, max=0.05)  # 小幅修正

            # 应用修正
            Pz_physics = Pz_physics * (1 + corrections[..., 0])
            S_physics = S_physics * (1 + corrections[..., 1])
            Yc_physics = Yc_physics * (1 + corrections[..., 2])

        return Pz_physics, S_physics, Yc_physics

# 兼容性别名，保持向后兼容
LandingGearPhysicsModule = EnhancedLandingGearPhysicsModule

class StreamlinedTransferPredictor(nn.Module):
    """
    精简的迁移学习预测器 - 整合所有有用组件

    特性:
    1. 支持2D输入(质量,高度)迁移学习和3D输出(pz,s,yc)
    2. 集成增强物理模块
    3. 自适应权重系统
    4. 导数特征工程
    5. 现代训练算法支持
    """

    def __init__(self, input_dim=2, hidden_dim=320, num_layers=4, dropout=0.12,
                 seq_len=100, use_physics=True, physics_weight=0.1):
        super(StreamlinedTransferPredictor, self).__init__()

        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.seq_len = seq_len
        self.use_physics = use_physics

        # 1. 增强特征工程模块
        self.feature_engineering = nn.ModuleDict({
            # 基础特征投影
            'basic_projection': nn.Sequential(
                nn.Linear(input_dim, hidden_dim//2),
                nn.LayerNorm(hidden_dim//2),
                nn.GELU(),
                nn.Dropout(dropout * 0.3)
            ),

            # 物理交互特征
            'physics_interaction': nn.Sequential(
                nn.Linear(6, hidden_dim//2),  # mass, height, mass*height, mass/height, sqrt(mass*height), mass^2/height
                nn.LayerNorm(hidden_dim//2),
                nn.GELU(),
                nn.Dropout(dropout * 0.3)
            ),

            # 导数特征处理 (如果提供)
            'derivative_features': nn.Sequential(
                nn.Linear(4, hidden_dim//4),  # dS/dt, d2S/dt2, dPz/dt, d2Pz/dt2
                nn.LayerNorm(hidden_dim//4),
                nn.GELU(),
                nn.Dropout(dropout * 0.3)
            ) if input_dim > 2 else None
        })

        # 2. 核心LSTM架构 (借鉴成功经验)
        lstm_input_dim = hidden_dim + (hidden_dim//4 if input_dim > 2 else 0)
        self.lstm = nn.LSTM(
            lstm_input_dim, hidden_dim//2,
            num_layers=num_layers,
            bidirectional=True,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0
        )

        # 3. 多头注意力机制
        self.attention = nn.MultiheadAttention(
            hidden_dim, num_heads=8, dropout=dropout * 0.5, batch_first=True
        )

        # 4. 增强物理约束模块
        if use_physics:
            self.physics_module = EnhancedLandingGearPhysicsModule(
                hidden_dim=hidden_dim//2, use_full_rk=False  # 使用简化版本提高速度
            )

        # 5. 自适应权重系统
        self.adaptive_weights = nn.ParameterDict({
            'physics_weight': nn.Parameter(torch.tensor(physics_weight)),
            'temporal_weight': nn.Parameter(torch.tensor(0.1)),
            'feature_weight': nn.Parameter(torch.tensor(0.8))
        })

        # 6. 专门的输出预测头
        self.output_heads = nn.ModuleDict({
            'S_head': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim//2),
                nn.LayerNorm(hidden_dim//2),
                nn.GELU(),
                nn.Dropout(dropout),
                nn.Linear(hidden_dim//2, hidden_dim//4),
                nn.GELU(),
                nn.Linear(hidden_dim//4, 1)
            ),
            'Pz_head': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim//2),
                nn.LayerNorm(hidden_dim//2),
                nn.GELU(),
                nn.Dropout(dropout),
                nn.Linear(hidden_dim//2, hidden_dim//4),
                nn.GELU(),
                nn.Linear(hidden_dim//4, 1)
            ),
            'Yc_head': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim//4),
                nn.LayerNorm(hidden_dim//4),
                nn.GELU(),
                nn.Dropout(dropout),
                nn.Linear(hidden_dim//4, 1)
            )
        })

        # 7. 损失权重配置
        self.loss_weights = {
            'S': 5.0,      # 重点优化S
            'Pz': 3.0,     # 重点优化Pz
            'Yc': 2.0,     # 适度优化Yc
            'physics': 0.2 # 物理一致性
        }

    def extract_physics_features(self, mass_norm, height_norm):
        """提取物理交互特征"""
        physics_features = torch.stack([
            mass_norm,
            height_norm,
            mass_norm * height_norm,
            mass_norm / (height_norm + 1e-8),
            torch.sqrt(mass_norm * height_norm + 1e-8),
            mass_norm**2 / (height_norm + 1e-8)
        ], dim=-1)
        return physics_features

    def forward(self, x, derivatives=None, epoch_ratio=0.5):
        """
        前向传播
        Args:
            x: [batch_size, seq_len, input_dim] - 输入特征 (质量, 高度, [其他传感器数据])
            derivatives: dict - 导数特征 (可选)
            epoch_ratio: 训练进度比例 (用于自适应权重)
        Returns:
            dict: 包含预测结果和中间信息
        """
        batch_size, seq_len, _ = x.shape
        device = x.device

        # 1. 特征工程
        mass_norm = x[:, :, 0]  # 归一化质量
        height_norm = x[:, :, 1]  # 归一化高度

        # 基础特征投影
        basic_features = self.feature_engineering['basic_projection'](x[:, :, :self.input_dim])

        # 物理交互特征
        physics_input = self.extract_physics_features(mass_norm, height_norm)
        physics_features = self.feature_engineering['physics_interaction'](physics_input)

        # 组合特征
        combined_features = torch.cat([basic_features, physics_features], dim=-1)

        # 导数特征 (如果提供且模型支持)
        if derivatives is not None and self.feature_engineering['derivative_features'] is not None:
            derivative_input = torch.stack([
                derivatives.get('dS_dt', torch.zeros(batch_size, seq_len, device=device)),
                derivatives.get('d2S_dt2', torch.zeros(batch_size, seq_len, device=device)),
                derivatives.get('dPz_dt', torch.zeros(batch_size, seq_len, device=device)),
                derivatives.get('d2Pz_dt2', torch.zeros(batch_size, seq_len, device=device))
            ], dim=-1)
            derivative_features = self.feature_engineering['derivative_features'](derivative_input)
            combined_features = torch.cat([combined_features, derivative_features], dim=-1)

        # 2. LSTM处理
        lstm_out, _ = self.lstm(combined_features)  # [B, L, hidden_dim]

        # 3. 注意力增强
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)

        # 4. 残差连接和特征融合
        enhanced_features = lstm_out + attn_out  # [B, L, hidden_dim]

        # 5. 神经网络预测
        S_nn = self.output_heads['S_head'](enhanced_features).squeeze(-1)  # [B, L]
        Pz_nn = self.output_heads['Pz_head'](enhanced_features).squeeze(-1)  # [B, L]
        Yc_nn = self.output_heads['Yc_head'](enhanced_features).squeeze(-1)  # [B, L]

        # 6. 物理约束预测 (如果启用)
        if self.use_physics:
            # 反归一化得到实际值
            mass_actual = mass_norm * 3000.0  # kg
            height_actual = height_norm * 3000.0  # mm

            # 物理模型预测 (使用批次中的平均值)
            mass_avg = mass_actual.mean(dim=1)  # [B]
            height_avg = height_actual.mean(dim=1)  # [B]

            Pz_physics, S_physics, Yc_physics = self.physics_module(height_avg, mass_avg)

            # 归一化物理预测
            pz_min, pz_max = -1.630496, 203.920620
            s_min, s_max = -536.617130, 45.976616
            yc_min, yc_max = 0, 100

            S_physics_norm = (S_physics - s_min) / (s_max - s_min + 1e-8)
            Pz_physics_norm = (Pz_physics - pz_min) / (pz_max - pz_min + 1e-8)
            Yc_physics_norm = Yc_physics / yc_max

            # 扩展到序列长度
            S_physics_expanded = S_physics_norm.unsqueeze(1).expand(-1, seq_len)
            Pz_physics_expanded = Pz_physics_norm.unsqueeze(1).expand(-1, seq_len)
            Yc_physics_expanded = Yc_physics_norm.unsqueeze(1).expand(-1, seq_len)
        else:
            S_physics_expanded = torch.zeros_like(S_nn)
            Pz_physics_expanded = torch.zeros_like(Pz_nn)
            Yc_physics_expanded = torch.zeros_like(Yc_nn)

        # 7. 自适应权重融合
        physics_weight = torch.sigmoid(self.adaptive_weights['physics_weight'])

        # 根据训练进度调整权重
        adaptive_physics_weight = physics_weight * (1.0 - 0.5 * epoch_ratio)  # 训练后期减少物理约束

        # 融合预测
        S_pred = adaptive_physics_weight * S_physics_expanded + (1 - adaptive_physics_weight) * S_nn
        Pz_pred = adaptive_physics_weight * Pz_physics_expanded + (1 - adaptive_physics_weight) * Pz_nn
        Yc_pred = adaptive_physics_weight * Yc_physics_expanded + (1 - adaptive_physics_weight) * Yc_nn

        # 8. 输出结果
        outputs = {
            'predictions': torch.stack([S_pred, Pz_pred, Yc_pred], dim=-1),  # [B, L, 3]
            'S': S_pred,
            'Pz': Pz_pred,
            'Yc': Yc_pred,
            'features': enhanced_features,
            # 分离的预测用于损失计算
            'S_nn': S_nn,
            'Pz_nn': Pz_nn,
            'Yc_nn': Yc_nn,
            'S_physics': S_physics_expanded if self.use_physics else None,
            'Pz_physics': Pz_physics_expanded if self.use_physics else None,
            'Yc_physics': Yc_physics_expanded if self.use_physics else None,
            # 权重信息
            'physics_weight': adaptive_physics_weight,
            'epoch_ratio': epoch_ratio
        }

        return outputs

    def compute_multitask_loss(self, outputs, targets, epoch_ratio=0.5, remove_physics_if_worse=True):
        """
        增强的多任务损失计算
        Args:
            outputs: 模型输出
            targets: 目标值 [B, L, 3] (S, Pz, Yc)
            epoch_ratio: 训练进度
            remove_physics_if_worse: 如果物理约束降低精度则移除
        Returns:
            total_loss, loss_dict
        """
        S_target, Pz_target, Yc_target = targets[..., 0], targets[..., 1], targets[..., 2]
        S_pred, Pz_pred, Yc_pred = outputs['S'], outputs['Pz'], outputs['Yc']

        # 1. 基础预测损失 (使用多种损失函数组合)
        def robust_loss(pred, target, alpha=0.5):
            """结合MSE和Smooth L1的鲁棒损失"""
            mse_loss = F.mse_loss(pred, target)
            smooth_l1_loss = F.smooth_l1_loss(pred, target)
            return alpha * mse_loss + (1 - alpha) * smooth_l1_loss

        loss_S = robust_loss(S_pred, S_target)
        loss_Pz = robust_loss(Pz_pred, Pz_target)
        loss_Yc = robust_loss(Yc_pred, Yc_target)

        # 2. 相关性损失 (确保预测与真实值相关)
        def correlation_loss(pred, target):
            pred_centered = pred - torch.mean(pred, dim=-1, keepdim=True)
            target_centered = target - torch.mean(target, dim=-1, keepdim=True)

            numerator = torch.sum(pred_centered * target_centered, dim=-1)
            pred_norm = torch.sqrt(torch.sum(pred_centered**2, dim=-1) + 1e-8)
            target_norm = torch.sqrt(torch.sum(target_centered**2, dim=-1) + 1e-8)

            correlation = numerator / (pred_norm * target_norm + 1e-8)
            return torch.mean(1.0 - torch.abs(correlation))

        corr_loss_S = correlation_loss(S_pred, S_target)
        corr_loss_Pz = correlation_loss(Pz_pred, Pz_target)
        corr_loss_Yc = correlation_loss(Yc_pred, Yc_target)

        # 3. 物理一致性损失 (如果启用物理约束)
        loss_physics = torch.tensor(0.0, device=targets.device)
        if self.use_physics and outputs['S_physics'] is not None:
            # 神经网络预测与物理预测的一致性
            physics_consistency_S = F.mse_loss(outputs['S_nn'], outputs['S_physics'].detach())
            physics_consistency_Pz = F.mse_loss(outputs['Pz_nn'], outputs['Pz_physics'].detach())
            physics_consistency_Yc = F.mse_loss(outputs['Yc_nn'], outputs['Yc_physics'].detach())

            loss_physics = (physics_consistency_S + physics_consistency_Pz + physics_consistency_Yc) / 3.0

            # 检查物理约束是否有帮助
            if remove_physics_if_worse and epoch_ratio > 0.3:
                # 计算纯神经网络损失
                nn_loss_S = robust_loss(outputs['S_nn'], S_target)
                nn_loss_Pz = robust_loss(outputs['Pz_nn'], Pz_target)
                nn_loss_Yc = robust_loss(outputs['Yc_nn'], Yc_target)
                nn_total_loss = nn_loss_S + nn_loss_Pz + nn_loss_Yc

                # 计算融合后损失
                fusion_total_loss = loss_S + loss_Pz + loss_Yc

                # 如果物理约束使性能变差，减少其权重
                if fusion_total_loss > nn_total_loss * 1.1:  # 10%的容忍度
                    loss_physics = loss_physics * 0.1  # 大幅减少物理约束权重

        # 4. 时间一致性损失 (相邻时间步的平滑性)
        def temporal_consistency_loss(pred):
            if pred.size(1) > 1:
                diff = pred[:, 1:] - pred[:, :-1]
                return torch.mean(torch.abs(diff))
            return torch.tensor(0.0, device=pred.device)

        temporal_loss_S = temporal_consistency_loss(S_pred)
        temporal_loss_Pz = temporal_consistency_loss(Pz_pred)
        temporal_loss_Yc = temporal_consistency_loss(Yc_pred)
        temporal_loss = (temporal_loss_S + temporal_loss_Pz + temporal_loss_Yc) / 3.0

        # 5. 动态权重调整
        progress_factor = min(1.0, epoch_ratio * 2.0)

        # S和Pz权重随训练进度增加 (重点优化)
        s_weight = self.loss_weights['S'] * (1.0 + 2.0 * progress_factor)
        pz_weight = self.loss_weights['Pz'] * (1.0 + 1.5 * progress_factor)
        yc_weight = self.loss_weights['Yc']
        physics_weight = self.loss_weights['physics'] * (1.0 - 0.3 * progress_factor)

        # 6. 组合损失
        total_loss = (
            s_weight * (loss_S + 0.3 * corr_loss_S) +
            pz_weight * (loss_Pz + 0.3 * corr_loss_Pz) +
            yc_weight * (loss_Yc + 0.2 * corr_loss_Yc) +
            physics_weight * loss_physics +
            0.1 * temporal_loss  # 时间一致性权重
        )

        # 7. 返回详细损失信息
        loss_dict = {
            'loss_S': loss_S.item(),
            'loss_Pz': loss_Pz.item(),
            'loss_Yc': loss_Yc.item(),
            'corr_loss_S': corr_loss_S.item(),
            'corr_loss_Pz': corr_loss_Pz.item(),
            'corr_loss_Yc': corr_loss_Yc.item(),
            'loss_physics': loss_physics.item(),
            'temporal_loss': temporal_loss.item(),
            'total_loss': total_loss.item(),
            's_weight': s_weight,
            'pz_weight': pz_weight,
            'yc_weight': yc_weight,
            'physics_weight': physics_weight,
            'physics_actual_weight': outputs.get('physics_weight', 0.0)
        }

        return total_loss, loss_dict

# 新的主要模型别名 - 推荐使用
EnhancedFinalBestPredictor = StreamlinedTransferPredictor

# ============================
# 优化的迁移学习训练器
# ============================

class OptimizedTransferLearningTrainer:
    """
    优化的迁移学习训练器

    特性:
    1. 两阶段训练: 全特征训练 -> 2D迁移学习
    2. 自动导数特征计算
    3. 自适应权重调整
    4. 现代训练算法集成
    """

    def __init__(self, device='cuda', target_accuracy=0.05):
        self.device = device
        self.target_accuracy = target_accuracy

        # 训练配置
        self.stage1_config = {
            'epochs': 100,
            'lr': 1e-3,
            'weight_decay': 1e-4,
            'patience': 20,
            'use_full_features': True
        }

        self.stage2_config = {
            'epochs': 50,
            'lr': 5e-4,
            'weight_decay': 1e-4,
            'patience': 15,
            'use_full_features': False
        }

    def compute_derivatives(self, data, dt=0.001):
        """
        计算导数特征 dS/dt, d2S/dt2, dPz/dt, d2Pz/dt2
        Args:
            data: [B, L, C] 时间序列数据
            dt: 时间步长
        Returns:
            dict: 导数特征字典
        """
        batch_size, seq_len, _ = data.shape
        device = data.device

        # 假设数据格式: [mass, height, ..., S, Pz, Yc, ...]
        # 需要根据实际数据格式调整索引
        if data.shape[-1] >= 5:  # 假设S在索引2，Pz在索引3
            S = data[:, :, 2]  # [B, L]
            Pz = data[:, :, 3]  # [B, L]

            # 计算一阶导数 (中心差分)
            dS_dt = torch.zeros_like(S)
            dPz_dt = torch.zeros_like(Pz)

            if seq_len > 2:
                dS_dt[:, 1:-1] = (S[:, 2:] - S[:, :-2]) / (2 * dt)
                dPz_dt[:, 1:-1] = (Pz[:, 2:] - Pz[:, :-2]) / (2 * dt)

                # 边界处理
                dS_dt[:, 0] = (S[:, 1] - S[:, 0]) / dt
                dS_dt[:, -1] = (S[:, -1] - S[:, -2]) / dt
                dPz_dt[:, 0] = (Pz[:, 1] - Pz[:, 0]) / dt
                dPz_dt[:, -1] = (Pz[:, -1] - Pz[:, -2]) / dt

            # 计算二阶导数
            d2S_dt2 = torch.zeros_like(S)
            d2Pz_dt2 = torch.zeros_like(Pz)

            if seq_len > 2:
                d2S_dt2[:, 1:-1] = (dS_dt[:, 2:] - dS_dt[:, :-2]) / (2 * dt)
                d2Pz_dt2[:, 1:-1] = (dPz_dt[:, 2:] - dPz_dt[:, :-2]) / (2 * dt)
        else:
            # 如果没有足够的特征，返回零
            dS_dt = torch.zeros(batch_size, seq_len, device=device)
            d2S_dt2 = torch.zeros(batch_size, seq_len, device=device)
            dPz_dt = torch.zeros(batch_size, seq_len, device=device)
            d2Pz_dt2 = torch.zeros(batch_size, seq_len, device=device)

        return {
            'dS_dt': dS_dt,
            'd2S_dt2': d2S_dt2,
            'dPz_dt': dPz_dt,
            'd2Pz_dt2': d2Pz_dt2
        }

    def create_model(self, input_dim, use_physics=True):
        """创建优化的模型"""
        model = StreamlinedTransferPredictor(
            input_dim=input_dim,
            hidden_dim=320,
            num_layers=4,
            dropout=0.12,
            seq_len=100,
            use_physics=use_physics,
            physics_weight=0.1
        ).to(self.device)
        return model

    def create_optimizer(self, model, lr, weight_decay):
        """创建现代优化器"""
        # 使用AdamW优化器，支持权重衰减
        optimizer = torch.optim.AdamW(
            model.parameters(),
            lr=lr,
            weight_decay=weight_decay,
            betas=(0.9, 0.999),
            eps=1e-8
        )
        return optimizer

    def create_scheduler(self, optimizer, epochs):
        """创建学习率调度器"""
        # 使用余弦退火调度器
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=epochs, eta_min=1e-6
        )
        return scheduler

    def train_stage1(self, train_loader, val_loader=None):
        """
        阶段1: 使用全部传感器数据训练
        """
        print("🚀 开始阶段1训练: 全特征学习")

        # 创建模型 (支持全特征)
        input_dim = 20  # 假设有20个传感器特征
        model = self.create_model(input_dim, use_physics=True)

        # 创建优化器和调度器
        optimizer = self.create_optimizer(model, **self.stage1_config)
        scheduler = self.create_scheduler(optimizer, self.stage1_config['epochs'])

        best_loss = float('inf')
        patience_counter = 0

        for epoch in range(self.stage1_config['epochs']):
            model.train()
            total_loss = 0
            num_batches = 0

            for batch_idx, (data, targets) in enumerate(train_loader):
                data, targets = data.to(self.device), targets.to(self.device)

                # 计算导数特征
                derivatives = self.compute_derivatives(data)

                # 前向传播
                optimizer.zero_grad()
                outputs = model(data, derivatives=derivatives, epoch_ratio=epoch/self.stage1_config['epochs'])

                # 计算损失
                loss, loss_dict = model.compute_multitask_loss(
                    outputs, targets, epoch_ratio=epoch/self.stage1_config['epochs']
                )

                # 反向传播
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                optimizer.step()

                total_loss += loss.item()
                num_batches += 1

            scheduler.step()
            avg_loss = total_loss / num_batches

            # 验证
            if val_loader is not None:
                val_loss = self.validate(model, val_loader)
                print(f"Epoch {epoch+1}/{self.stage1_config['epochs']}: "
                      f"Train Loss: {avg_loss:.6f}, Val Loss: {val_loss:.6f}")

                # 早停检查
                if val_loss < best_loss:
                    best_loss = val_loss
                    patience_counter = 0
                    # 保存最佳模型
                    torch.save(model.state_dict(), 'best_stage1_model.pth')
                else:
                    patience_counter += 1
                    if patience_counter >= self.stage1_config['patience']:
                        print(f"早停在epoch {epoch+1}")
                        break
            else:
                print(f"Epoch {epoch+1}/{self.stage1_config['epochs']}: Train Loss: {avg_loss:.6f}")

        # 加载最佳模型
        if val_loader is not None:
            model.load_state_dict(torch.load('best_stage1_model.pth'))

        return model

    def train_stage2(self, stage1_model, transfer_loader, val_loader=None):
        """
        阶段2: 迁移学习，仅使用质量和高度
        """
        print("🎯 开始阶段2训练: 2D迁移学习 (质量+高度 -> 3D输出)")

        # 创建迁移学习模型 (仅2D输入)
        transfer_model = self.create_model(input_dim=2, use_physics=True)

        # 权重迁移 (从stage1模型复制兼容的权重)
        self.transfer_weights(stage1_model, transfer_model)

        # 创建优化器和调度器
        optimizer = self.create_optimizer(transfer_model, **self.stage2_config)
        scheduler = self.create_scheduler(optimizer, self.stage2_config['epochs'])

        best_loss = float('inf')
        patience_counter = 0

        for epoch in range(self.stage2_config['epochs']):
            transfer_model.train()
            total_loss = 0
            num_batches = 0

            for batch_idx, (data, targets) in enumerate(transfer_loader):
                # 只使用质量和高度 (前2个特征)
                transfer_data = data[:, :, :2].to(self.device)
                targets = targets.to(self.device)

                # 前向传播
                optimizer.zero_grad()
                outputs = transfer_model(transfer_data, epoch_ratio=epoch/self.stage2_config['epochs'])

                # 计算损失
                loss, loss_dict = transfer_model.compute_multitask_loss(
                    outputs, targets, epoch_ratio=epoch/self.stage2_config['epochs']
                )

                # 反向传播
                loss.backward()
                torch.nn.utils.clip_grad_norm_(transfer_model.parameters(), 1.0)
                optimizer.step()

                total_loss += loss.item()
                num_batches += 1

            scheduler.step()
            avg_loss = total_loss / num_batches

            # 验证
            if val_loader is not None:
                val_loss = self.validate_transfer(transfer_model, val_loader)
                print(f"Epoch {epoch+1}/{self.stage2_config['epochs']}: "
                      f"Train Loss: {avg_loss:.6f}, Val Loss: {val_loss:.6f}")

                # 早停检查
                if val_loss < best_loss:
                    best_loss = val_loss
                    patience_counter = 0
                    # 保存最佳模型
                    torch.save(transfer_model.state_dict(), 'best_transfer_model.pth')
                else:
                    patience_counter += 1
                    if patience_counter >= self.stage2_config['patience']:
                        print(f"早停在epoch {epoch+1}")
                        break
            else:
                print(f"Epoch {epoch+1}/{self.stage2_config['epochs']}: Train Loss: {avg_loss:.6f}")

        # 加载最佳模型
        if val_loader is not None:
            transfer_model.load_state_dict(torch.load('best_transfer_model.pth'))

        return transfer_model

    def transfer_weights(self, source_model, target_model):
        """
        权重迁移：从全特征模型迁移到2D输入模型
        """
        source_dict = source_model.state_dict()
        target_dict = target_model.state_dict()

        # 迁移兼容的权重
        transferred_keys = []
        for key in target_dict.keys():
            if key in source_dict and target_dict[key].shape == source_dict[key].shape:
                target_dict[key] = source_dict[key].clone()
                transferred_keys.append(key)

        target_model.load_state_dict(target_dict)
        print(f"✅ 成功迁移 {len(transferred_keys)} 个权重参数")

    def validate(self, model, val_loader):
        """验证阶段1模型"""
        model.eval()
        total_loss = 0
        num_batches = 0

        with torch.no_grad():
            for data, targets in val_loader:
                data, targets = data.to(self.device), targets.to(self.device)

                derivatives = self.compute_derivatives(data)
                outputs = model(data, derivatives=derivatives)
                loss, _ = model.compute_multitask_loss(outputs, targets)

                total_loss += loss.item()
                num_batches += 1

        return total_loss / num_batches

    def validate_transfer(self, model, val_loader):
        """验证迁移学习模型"""
        model.eval()
        total_loss = 0
        num_batches = 0

        with torch.no_grad():
            for data, targets in val_loader:
                transfer_data = data[:, :, :2].to(self.device)
                targets = targets.to(self.device)

                outputs = model(transfer_data)
                loss, _ = model.compute_multitask_loss(outputs, targets)

                total_loss += loss.item()
                num_batches += 1

        return total_loss / num_batches

    def evaluate_accuracy(self, model, test_loader, use_transfer=True):
        """
        评估模型精度
        Returns:
            dict: 包含各变量的MAPE误差
        """
        model.eval()
        all_predictions = []
        all_targets = []

        with torch.no_grad():
            for data, targets in test_loader:
                if use_transfer:
                    test_data = data[:, :, :2].to(self.device)
                else:
                    test_data = data.to(self.device)

                targets = targets.to(self.device)
                outputs = model(test_data)

                predictions = outputs['predictions']  # [B, L, 3]

                all_predictions.append(predictions.cpu())
                all_targets.append(targets.cpu())

        # 合并所有批次
        all_predictions = torch.cat(all_predictions, dim=0)  # [N, L, 3]
        all_targets = torch.cat(all_targets, dim=0)  # [N, L, 3]

        # 计算MAPE误差
        def mape(pred, target):
            return torch.mean(torch.abs((pred - target) / (target + 1e-8))) * 100

        s_mape = mape(all_predictions[:, :, 0], all_targets[:, :, 0])
        pz_mape = mape(all_predictions[:, :, 1], all_targets[:, :, 1])
        yc_mape = mape(all_predictions[:, :, 2], all_targets[:, :, 2])
        avg_mape = (s_mape + pz_mape + yc_mape) / 3

        return {
            'S_MAPE': s_mape.item(),
            'Pz_MAPE': pz_mape.item(),
            'Yc_MAPE': yc_mape.item(),
            'Average_MAPE': avg_mape.item()
        }

# ============================
# 增强的自适应权重控制器
# ============================

class AdaptiveWeightController:
    """
    自适应权重控制器

    功能:
    1. 动态调整物理约束权重
    2. 基于性能反馈调整损失权重
    3. 自动移除有害的物理约束
    4. 支持多种自适应策略
    """

    def __init__(self, initial_physics_weight=0.1, target_accuracy=0.05):
        self.initial_physics_weight = initial_physics_weight
        self.target_accuracy = target_accuracy

        # 权重历史记录
        self.physics_weight_history = []
        self.loss_history = []
        self.accuracy_history = []

        # 自适应参数
        self.adaptation_rate = 0.1
        self.min_physics_weight = 0.0
        self.max_physics_weight = 0.5

        # 性能监控
        self.performance_window = 10  # 监控窗口大小
        self.improvement_threshold = 0.01  # 改进阈值

    def update_weights(self, model, current_loss, current_accuracy, epoch_ratio):
        """
        更新自适应权重
        Args:
            model: 当前模型
            current_loss: 当前损失
            current_accuracy: 当前精度 (MAPE)
            epoch_ratio: 训练进度比例
        Returns:
            dict: 更新后的权重配置
        """
        # 记录历史
        self.loss_history.append(current_loss)
        self.accuracy_history.append(current_accuracy)

        # 计算基础物理权重
        base_physics_weight = self.initial_physics_weight * (1.0 - 0.5 * epoch_ratio)

        # 策略1: 基于精度的自适应调整
        if len(self.accuracy_history) >= 2:
            accuracy_trend = self.accuracy_history[-1] - self.accuracy_history[-2]

            if accuracy_trend > 0:  # 精度下降
                base_physics_weight *= 0.9  # 减少物理约束
            elif accuracy_trend < -self.improvement_threshold:  # 精度显著提升
                base_physics_weight *= 1.1  # 增加物理约束

        # 策略2: 基于性能窗口的调整
        if len(self.accuracy_history) >= self.performance_window:
            recent_accuracies = self.accuracy_history[-self.performance_window:]
            accuracy_std = torch.std(torch.tensor(recent_accuracies)).item()

            if accuracy_std > 0.02:  # 精度不稳定
                base_physics_weight *= 0.8  # 减少物理约束以提高稳定性

        # 策略3: 目标精度导向调整
        if current_accuracy > self.target_accuracy * 2:  # 远离目标
            base_physics_weight *= 0.7  # 大幅减少物理约束，让神经网络主导
        elif current_accuracy < self.target_accuracy * 1.2:  # 接近目标
            base_physics_weight *= 1.2  # 适度增加物理约束以提高泛化

        # 限制权重范围
        final_physics_weight = torch.clamp(torch.tensor(base_physics_weight),
                                          self.min_physics_weight,
                                          self.max_physics_weight).item()

        # 更新模型权重
        if hasattr(model, 'adaptive_weights'):
            with torch.no_grad():
                model.adaptive_weights['physics_weight'].data = torch.tensor(final_physics_weight)

        # 动态损失权重调整
        loss_weights = self._compute_dynamic_loss_weights(current_accuracy, epoch_ratio)

        self.physics_weight_history.append(final_physics_weight)

        return {
            'physics_weight': final_physics_weight,
            'loss_weights': loss_weights,
            'adaptation_info': {
                'accuracy_trend': accuracy_trend if len(self.accuracy_history) >= 2 else 0,
                'stability': accuracy_std if len(self.accuracy_history) >= self.performance_window else 0,
                'target_distance': abs(current_accuracy - self.target_accuracy)
            }
        }

    def _compute_dynamic_loss_weights(self, current_accuracy, epoch_ratio):
        """计算动态损失权重"""
        # 基础权重
        base_weights = {'S': 5.0, 'Pz': 3.0, 'Yc': 2.0}

        # 根据当前精度调整
        if current_accuracy > self.target_accuracy * 1.5:
            # 精度较差，增加重点变量权重
            base_weights['S'] *= 1.5
            base_weights['Pz'] *= 1.3
        elif current_accuracy < self.target_accuracy * 1.1:
            # 精度较好，平衡各变量权重
            base_weights['S'] *= 0.9
            base_weights['Pz'] *= 0.9
            base_weights['Yc'] *= 1.1

        # 根据训练进度调整
        progress_factor = min(1.0, epoch_ratio * 2.0)
        base_weights['S'] *= (1.0 + progress_factor)
        base_weights['Pz'] *= (1.0 + 0.8 * progress_factor)

        return base_weights

    def should_remove_physics(self, model, val_loader, device):
        """
        判断是否应该移除物理约束
        通过A/B测试比较有无物理约束的性能
        """
        if len(self.accuracy_history) < 5:  # 需要足够的历史数据
            return False

        # 临时禁用物理约束进行测试
        original_use_physics = model.use_physics
        model.use_physics = False

        # 评估无物理约束的性能
        no_physics_accuracy = self._evaluate_model_accuracy(model, val_loader, device)

        # 恢复物理约束
        model.use_physics = original_use_physics

        # 评估有物理约束的性能
        with_physics_accuracy = self._evaluate_model_accuracy(model, val_loader, device)

        # 如果无物理约束性能更好，建议移除
        improvement = with_physics_accuracy - no_physics_accuracy

        return improvement > 0.01  # 如果物理约束使性能下降超过1%，建议移除

    def _evaluate_model_accuracy(self, model, val_loader, device):
        """快速评估模型精度"""
        model.eval()
        total_error = 0
        num_samples = 0

        with torch.no_grad():
            for i, (data, targets) in enumerate(val_loader):
                if i >= 3:  # 只评估前几个批次以节省时间
                    break

                data = data[:, :, :2].to(device)  # 使用2D输入
                targets = targets.to(device)

                outputs = model(data)
                predictions = outputs['predictions']

                # 计算MAPE
                error = torch.mean(torch.abs((predictions - targets) / (targets + 1e-8)))
                total_error += error.item()
                num_samples += 1

        return total_error / num_samples if num_samples > 0 else float('inf')

    def get_status_report(self):
        """获取自适应权重状态报告"""
        if not self.physics_weight_history:
            return "No adaptation history available"

        current_weight = self.physics_weight_history[-1]
        weight_trend = "stable"

        if len(self.physics_weight_history) >= 2:
            recent_change = self.physics_weight_history[-1] - self.physics_weight_history[-2]
            if recent_change > 0.01:
                weight_trend = "increasing"
            elif recent_change < -0.01:
                weight_trend = "decreasing"

        return {
            'current_physics_weight': current_weight,
            'weight_trend': weight_trend,
            'adaptation_count': len(self.physics_weight_history),
            'current_accuracy': self.accuracy_history[-1] if self.accuracy_history else None,
            'target_accuracy': self.target_accuracy
        }

# ============================
# 增强的损失函数集合
# ============================

class AdvancedLossFunctions:
    """
    高级损失函数集合

    包含多种损失函数用于提高模型性能:
    1. 鲁棒损失函数
    2. 分布匹配损失
    3. 时序一致性损失
    4. 物理一致性损失
    5. 对抗性损失
    """

    @staticmethod
    def focal_mse_loss(pred, target, alpha=2.0, gamma=2.0):
        """
        Focal MSE损失 - 关注困难样本
        """
        mse = F.mse_loss(pred, target, reduction='none')
        focal_weight = torch.pow(mse, gamma)
        return torch.mean(alpha * focal_weight * mse)

    @staticmethod
    def huber_loss(pred, target, delta=1.0):
        """
        Huber损失 - 对异常值鲁棒
        """
        residual = torch.abs(pred - target)
        condition = residual < delta
        squared_loss = 0.5 * residual ** 2
        linear_loss = delta * residual - 0.5 * delta ** 2
        return torch.mean(torch.where(condition, squared_loss, linear_loss))

    @staticmethod
    def quantile_loss(pred, target, quantile=0.5):
        """
        分位数损失 - 用于不确定性估计
        """
        residual = target - pred
        loss = torch.max(quantile * residual, (quantile - 1) * residual)
        return torch.mean(loss)

    @staticmethod
    def distribution_matching_loss(pred, target):
        """
        分布匹配损失 - 确保预测分布与真实分布匹配
        """
        # 计算统计矩
        pred_mean = torch.mean(pred, dim=-1)
        target_mean = torch.mean(target, dim=-1)
        pred_std = torch.std(pred, dim=-1)
        target_std = torch.std(target, dim=-1)

        # 均值和方差匹配
        mean_loss = F.mse_loss(pred_mean, target_mean)
        std_loss = F.mse_loss(pred_std, target_std)

        return mean_loss + 0.5 * std_loss

    @staticmethod
    def temporal_consistency_loss(pred, alpha=0.1):
        """
        时序一致性损失 - 确保时间序列的平滑性
        """
        if pred.size(1) <= 1:
            return torch.tensor(0.0, device=pred.device)

        # 一阶差分
        first_diff = pred[:, 1:] - pred[:, :-1]
        first_order_loss = torch.mean(torch.abs(first_diff))

        # 二阶差分 (加速度)
        if pred.size(1) > 2:
            second_diff = first_diff[:, 1:] - first_diff[:, :-1]
            second_order_loss = torch.mean(torch.abs(second_diff))
            return first_order_loss + alpha * second_order_loss

        return first_order_loss

    @staticmethod
    def physics_consistency_loss(pred_dict, physics_dict, consistency_type='mse'):
        """
        物理一致性损失 - 确保预测与物理模型一致
        """
        if physics_dict is None:
            return torch.tensor(0.0)

        total_loss = torch.tensor(0.0, device=list(pred_dict.values())[0].device)
        count = 0

        for key in ['S', 'Pz', 'Yc']:
            if key in pred_dict and key in physics_dict:
                pred_val = pred_dict[key]
                physics_val = physics_dict[key]

                if consistency_type == 'mse':
                    loss = F.mse_loss(pred_val, physics_val.detach())
                elif consistency_type == 'kl':
                    # KL散度 (需要归一化到概率分布)
                    pred_prob = F.softmax(pred_val.flatten(), dim=0)
                    physics_prob = F.softmax(physics_val.flatten(), dim=0)
                    loss = F.kl_div(pred_prob.log(), physics_prob, reduction='mean')
                else:
                    loss = F.smooth_l1_loss(pred_val, physics_val.detach())

                total_loss += loss
                count += 1

        return total_loss / count if count > 0 else total_loss

    @staticmethod
    def gradient_penalty_loss(pred, target):
        """
        梯度惩罚损失 - 确保预测的梯度合理
        """
        if pred.requires_grad:
            grad_pred = torch.autograd.grad(
                outputs=pred.sum(), inputs=pred,
                create_graph=True, retain_graph=True, only_inputs=True
            )[0]

            grad_norm = torch.norm(grad_pred, dim=-1)
            penalty = torch.mean((grad_norm - 1) ** 2)
            return penalty

        return torch.tensor(0.0, device=pred.device)

    @staticmethod
    def adaptive_weighted_loss(losses, weights, temperature=1.0):
        """
        自适应加权损失 - 动态调整各损失项权重
        """
        # 使用softmax进行权重归一化
        normalized_weights = F.softmax(torch.tensor(weights) / temperature, dim=0)

        total_loss = torch.tensor(0.0)
        for loss, weight in zip(losses, normalized_weights):
            total_loss += weight * loss

        return total_loss

class EnhancedLossComputer:
    """
    增强的损失计算器 - 整合多种损失函数
    """

    def __init__(self, loss_config=None):
        self.loss_config = loss_config or {
            'use_focal': True,
            'use_huber': True,
            'use_distribution_matching': True,
            'use_temporal_consistency': True,
            'use_physics_consistency': True,
            'focal_alpha': 2.0,
            'focal_gamma': 2.0,
            'huber_delta': 1.0,
            'temporal_alpha': 0.1,
            'physics_consistency_type': 'mse'
        }

        self.loss_functions = AdvancedLossFunctions()

    def compute_comprehensive_loss(self, outputs, targets, epoch_ratio=0.5):
        """
        计算综合损失
        """
        S_target, Pz_target, Yc_target = targets[..., 0], targets[..., 1], targets[..., 2]
        S_pred, Pz_pred, Yc_pred = outputs['S'], outputs['Pz'], outputs['Yc']

        losses = {}

        # 1. 基础预测损失
        if self.loss_config['use_focal']:
            losses['focal_S'] = self.loss_functions.focal_mse_loss(
                S_pred, S_target,
                self.loss_config['focal_alpha'],
                self.loss_config['focal_gamma']
            )
            losses['focal_Pz'] = self.loss_functions.focal_mse_loss(
                Pz_pred, Pz_target,
                self.loss_config['focal_alpha'],
                self.loss_config['focal_gamma']
            )
            losses['focal_Yc'] = self.loss_functions.focal_mse_loss(
                Yc_pred, Yc_target,
                self.loss_config['focal_alpha'],
                self.loss_config['focal_gamma']
            )

        if self.loss_config['use_huber']:
            losses['huber_S'] = self.loss_functions.huber_loss(
                S_pred, S_target, self.loss_config['huber_delta']
            )
            losses['huber_Pz'] = self.loss_functions.huber_loss(
                Pz_pred, Pz_target, self.loss_config['huber_delta']
            )
            losses['huber_Yc'] = self.loss_functions.huber_loss(
                Yc_pred, Yc_target, self.loss_config['huber_delta']
            )

        # 2. 分布匹配损失
        if self.loss_config['use_distribution_matching']:
            losses['dist_S'] = self.loss_functions.distribution_matching_loss(S_pred, S_target)
            losses['dist_Pz'] = self.loss_functions.distribution_matching_loss(Pz_pred, Pz_target)
            losses['dist_Yc'] = self.loss_functions.distribution_matching_loss(Yc_pred, Yc_target)

        # 3. 时序一致性损失
        if self.loss_config['use_temporal_consistency']:
            losses['temporal_S'] = self.loss_functions.temporal_consistency_loss(
                S_pred, self.loss_config['temporal_alpha']
            )
            losses['temporal_Pz'] = self.loss_functions.temporal_consistency_loss(
                Pz_pred, self.loss_config['temporal_alpha']
            )
            losses['temporal_Yc'] = self.loss_functions.temporal_consistency_loss(
                Yc_pred, self.loss_config['temporal_alpha']
            )

        # 4. 物理一致性损失
        if self.loss_config['use_physics_consistency'] and 'S_physics' in outputs:
            physics_dict = {
                'S': outputs.get('S_physics'),
                'Pz': outputs.get('Pz_physics'),
                'Yc': outputs.get('Yc_physics')
            }
            pred_dict = {'S': S_pred, 'Pz': Pz_pred, 'Yc': Yc_pred}

            losses['physics_consistency'] = self.loss_functions.physics_consistency_loss(
                pred_dict, physics_dict, self.loss_config['physics_consistency_type']
            )

        return losses

# ============================
# 现代训练算法集合
# ============================

class ModernTrainingAlgorithms:
    """
    现代训练算法集合

    包含:
    1. 高级优化器 (AdamW, RAdam, Lookahead等)
    2. 学习率调度策略
    3. 正则化技术
    4. 训练技巧
    """

    @staticmethod
    def create_advanced_optimizer(model, optimizer_type='adamw', lr=1e-3, weight_decay=1e-4):
        """
        创建高级优化器
        """
        if optimizer_type.lower() == 'adamw':
            optimizer = torch.optim.AdamW(
                model.parameters(),
                lr=lr,
                weight_decay=weight_decay,
                betas=(0.9, 0.999),
                eps=1e-8,
                amsgrad=True  # 使用AMSGrad变体
            )
        elif optimizer_type.lower() == 'radam':
            # 需要安装: pip install torch-optimizer
            try:
                import torch_optimizer as optim_extra
                optimizer = optim_extra.RAdam(
                    model.parameters(),
                    lr=lr,
                    weight_decay=weight_decay,
                    betas=(0.9, 0.999),
                    eps=1e-8
                )
            except ImportError:
                print("torch-optimizer not installed, falling back to AdamW")
                optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
        elif optimizer_type.lower() == 'lamb':
            try:
                import torch_optimizer as optim_extra
                optimizer = optim_extra.Lamb(
                    model.parameters(),
                    lr=lr,
                    weight_decay=weight_decay,
                    betas=(0.9, 0.999),
                    eps=1e-8
                )
            except ImportError:
                print("torch-optimizer not installed, falling back to AdamW")
                optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
        else:
            # 默认使用AdamW
            optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)

        return optimizer

    @staticmethod
    def create_advanced_scheduler(optimizer, scheduler_type='cosine_warmup', epochs=100, warmup_epochs=10):
        """
        创建高级学习率调度器
        """
        if scheduler_type == 'cosine_warmup':
            # 带预热的余弦退火
            def lr_lambda(epoch):
                if epoch < warmup_epochs:
                    return epoch / warmup_epochs
                else:
                    progress = (epoch - warmup_epochs) / (epochs - warmup_epochs)
                    return 0.5 * (1 + math.cos(math.pi * progress))

            scheduler = torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)

        elif scheduler_type == 'polynomial':
            # 多项式衰减
            scheduler = torch.optim.lr_scheduler.PolynomialLR(
                optimizer, total_iters=epochs, power=0.9
            )

        elif scheduler_type == 'one_cycle':
            # One Cycle学习率策略
            scheduler = torch.optim.lr_scheduler.OneCycleLR(
                optimizer, max_lr=optimizer.param_groups[0]['lr'] * 10,
                epochs=epochs, steps_per_epoch=1
            )

        elif scheduler_type == 'reduce_on_plateau':
            # 基于验证损失的自适应调度
            scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
                optimizer, mode='min', factor=0.5, patience=10, verbose=True
            )

        else:
            # 默认余弦退火
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer, T_max=epochs, eta_min=1e-6
            )

        return scheduler

    @staticmethod
    def apply_gradient_clipping(model, clip_type='norm', clip_value=1.0):
        """
        应用梯度裁剪
        """
        if clip_type == 'norm':
            torch.nn.utils.clip_grad_norm_(model.parameters(), clip_value)
        elif clip_type == 'value':
            torch.nn.utils.clip_grad_value_(model.parameters(), clip_value)

    @staticmethod
    def apply_weight_decay_exclusion(model, exclude_bias=True, exclude_norm=True):
        """
        应用权重衰减排除策略
        """
        decay_params = []
        no_decay_params = []

        for name, param in model.named_parameters():
            if not param.requires_grad:
                continue

            # 排除偏置项
            if exclude_bias and 'bias' in name:
                no_decay_params.append(param)
            # 排除归一化层
            elif exclude_norm and ('norm' in name.lower() or 'bn' in name.lower()):
                no_decay_params.append(param)
            else:
                decay_params.append(param)

        return [
            {'params': decay_params, 'weight_decay': 1e-4},
            {'params': no_decay_params, 'weight_decay': 0.0}
        ]

class AdvancedTrainingTechniques:
    """
    高级训练技巧
    """

    def __init__(self, model, device='cuda'):
        self.model = model
        self.device = device

        # 训练状态
        self.best_loss = float('inf')
        self.patience_counter = 0
        self.learning_rate_history = []
        self.loss_history = []

        # EMA (指数移动平均)
        self.ema_decay = 0.999
        self.ema_model = None

    def setup_ema(self):
        """设置指数移动平均模型"""
        self.ema_model = torch.optim.swa_utils.AveragedModel(
            self.model, avg_fn=lambda avg_param, param, num_averaged:
            self.ema_decay * avg_param + (1 - self.ema_decay) * param
        )

    def update_ema(self):
        """更新EMA模型"""
        if self.ema_model is not None:
            self.ema_model.update_parameters(self.model)

    def apply_mixup(self, data, targets, alpha=0.2):
        """
        应用Mixup数据增强
        """
        if alpha > 0:
            lam = torch.distributions.Beta(alpha, alpha).sample().item()
        else:
            lam = 1

        batch_size = data.size(0)
        index = torch.randperm(batch_size).to(data.device)

        mixed_data = lam * data + (1 - lam) * data[index, :]
        mixed_targets = lam * targets + (1 - lam) * targets[index, :]

        return mixed_data, mixed_targets

    def apply_cutmix(self, data, targets, alpha=1.0):
        """
        应用CutMix数据增强 (适用于时间序列)
        """
        batch_size, seq_len, features = data.shape

        if alpha > 0:
            lam = torch.distributions.Beta(alpha, alpha).sample().item()
        else:
            lam = 1

        # 随机选择切割区域
        cut_len = int(seq_len * (1 - lam))
        cut_start = torch.randint(0, seq_len - cut_len + 1, (1,)).item()
        cut_end = cut_start + cut_len

        # 随机选择替换的样本
        index = torch.randperm(batch_size).to(data.device)

        # 应用CutMix
        mixed_data = data.clone()
        mixed_data[:, cut_start:cut_end, :] = data[index, cut_start:cut_end, :]

        # 调整标签权重
        mixed_targets = lam * targets + (1 - lam) * targets[index, :]

        return mixed_data, mixed_targets

    def apply_label_smoothing(self, targets, smoothing=0.1):
        """
        应用标签平滑 (适用于回归任务的变体)
        """
        # 对于回归任务，添加小量噪声
        noise = torch.randn_like(targets) * smoothing * torch.std(targets, dim=0, keepdim=True)
        smoothed_targets = targets + noise
        return smoothed_targets

    def compute_sam_gradient(self, loss_fn, inputs, targets, rho=0.05):
        """
        计算SAM (Sharpness-Aware Minimization) 梯度
        """
        # 第一步：计算标准梯度
        loss = loss_fn(inputs, targets)
        loss.backward()

        # 保存原始参数
        original_params = {}
        for name, param in self.model.named_parameters():
            if param.grad is not None:
                original_params[name] = param.data.clone()

        # 第二步：在梯度方向上扰动参数
        with torch.no_grad():
            for name, param in self.model.named_parameters():
                if param.grad is not None:
                    grad_norm = param.grad.norm()
                    if grad_norm > 0:
                        param.data += rho * param.grad / grad_norm

        # 第三步：计算扰动后的梯度
        self.model.zero_grad()
        perturbed_loss = loss_fn(inputs, targets)
        perturbed_loss.backward()

        # 恢复原始参数
        with torch.no_grad():
            for name, param in self.model.named_parameters():
                if name in original_params:
                    param.data = original_params[name]

        return perturbed_loss

    def early_stopping_check(self, val_loss, patience=20):
        """
        早停检查
        """
        if val_loss < self.best_loss:
            self.best_loss = val_loss
            self.patience_counter = 0
            return False  # 继续训练
        else:
            self.patience_counter += 1
            return self.patience_counter >= patience  # 是否应该停止

    def get_training_stats(self):
        """
        获取训练统计信息
        """
        return {
            'best_loss': self.best_loss,
            'patience_counter': self.patience_counter,
            'current_lr': self.learning_rate_history[-1] if self.learning_rate_history else None,
            'loss_trend': 'improving' if len(self.loss_history) >= 2 and
                         self.loss_history[-1] < self.loss_history[-2] else 'stable_or_degrading'
        }

# ============================
# 综合测试框架
# ============================

class ComprehensiveTestingFramework:
    """
    综合测试框架

    功能:
    1. 多变量性能评估 (pz, s, yc)
    2. 真实值vs预测值对比
    3. 多种精度指标计算
    4. 可视化结果生成
    5. 模型对比分析
    """

    def __init__(self, device='cuda'):
        self.device = device
        self.results_history = []

    def evaluate_model_comprehensive(self, model, test_loader, model_name="Model"):
        """
        综合评估模型性能
        """
        model.eval()

        all_predictions = {'S': [], 'Pz': [], 'Yc': []}
        all_targets = {'S': [], 'Pz': [], 'Yc': []}
        all_features = []

        print(f"🔍 开始评估模型: {model_name}")

        with torch.no_grad():
            for batch_idx, (data, targets) in enumerate(test_loader):
                # 使用2D输入进行迁移学习测试
                test_data = data[:, :, :2].to(self.device)
                targets = targets.to(self.device)

                # 模型预测
                outputs = model(test_data)
                predictions = outputs['predictions']  # [B, L, 3]

                # 分离各变量
                S_pred = predictions[:, :, 0].cpu()
                Pz_pred = predictions[:, :, 1].cpu()
                Yc_pred = predictions[:, :, 2].cpu()

                S_target = targets[:, :, 0].cpu()
                Pz_target = targets[:, :, 1].cpu()
                Yc_target = targets[:, :, 2].cpu()

                # 收集结果
                all_predictions['S'].append(S_pred)
                all_predictions['Pz'].append(Pz_pred)
                all_predictions['Yc'].append(Yc_pred)

                all_targets['S'].append(S_target)
                all_targets['Pz'].append(Pz_target)
                all_targets['Yc'].append(Yc_target)

                all_features.append(test_data.cpu())

        # 合并所有批次
        for var in ['S', 'Pz', 'Yc']:
            all_predictions[var] = torch.cat(all_predictions[var], dim=0)
            all_targets[var] = torch.cat(all_targets[var], dim=0)

        all_features = torch.cat(all_features, dim=0)

        # 计算各种指标
        metrics = self.compute_comprehensive_metrics(all_predictions, all_targets)

        # 生成详细报告
        report = self.generate_evaluation_report(
            all_predictions, all_targets, metrics, model_name
        )

        # 保存结果
        result_record = {
            'model_name': model_name,
            'metrics': metrics,
            'predictions': all_predictions,
            'targets': all_targets,
            'features': all_features,
            'report': report
        }

        self.results_history.append(result_record)

        return result_record

    def compute_comprehensive_metrics(self, predictions, targets):
        """
        计算综合指标
        """
        metrics = {}

        for var in ['S', 'Pz', 'Yc']:
            pred = predictions[var].flatten()
            target = targets[var].flatten()

            # 基础指标
            mae = torch.mean(torch.abs(pred - target)).item()
            mse = torch.mean((pred - target) ** 2).item()
            rmse = math.sqrt(mse)

            # MAPE (平均绝对百分比误差)
            mape = torch.mean(torch.abs((pred - target) / (target + 1e-8))).item() * 100

            # R² 决定系数
            ss_res = torch.sum((target - pred) ** 2)
            ss_tot = torch.sum((target - torch.mean(target)) ** 2)
            r2 = (1 - ss_res / ss_tot).item()

            # 相关系数
            correlation = torch.corrcoef(torch.stack([pred, target]))[0, 1].item()

            # 最大误差
            max_error = torch.max(torch.abs(pred - target)).item()

            # 分位数误差
            errors = torch.abs(pred - target)
            q25_error = torch.quantile(errors, 0.25).item()
            q50_error = torch.quantile(errors, 0.50).item()
            q75_error = torch.quantile(errors, 0.75).item()
            q95_error = torch.quantile(errors, 0.95).item()

            metrics[var] = {
                'MAE': mae,
                'MSE': mse,
                'RMSE': rmse,
                'MAPE': mape,
                'R2': r2,
                'Correlation': correlation,
                'Max_Error': max_error,
                'Q25_Error': q25_error,
                'Q50_Error': q50_error,
                'Q75_Error': q75_error,
                'Q95_Error': q95_error
            }

        # 整体指标
        mape_values = [metrics[var]['MAPE'] for var in ['S', 'Pz', 'Yc']]
        r2_values = [metrics[var]['R2'] for var in ['S', 'Pz', 'Yc']]
        corr_values = [metrics[var]['Correlation'] for var in ['S', 'Pz', 'Yc']]

        avg_mape = sum(mape_values) / len(mape_values)
        avg_r2 = sum(r2_values) / len(r2_values)
        avg_correlation = sum(corr_values) / len(corr_values)

        metrics['Overall'] = {
            'Average_MAPE': avg_mape,
            'Average_R2': avg_r2,
            'Average_Correlation': avg_correlation,
            'Target_Achievement': avg_mape <= 5.0  # 5%目标
        }

        return metrics

    def generate_evaluation_report(self, predictions, targets, metrics, model_name):
        """
        生成评估报告
        """
        report = f"""
        ========================================
        模型评估报告: {model_name}
        ========================================

        📊 整体性能:
        - 平均MAPE: {metrics['Overall']['Average_MAPE']:.2f}%
        - 平均R²: {metrics['Overall']['Average_R2']:.4f}
        - 平均相关系数: {metrics['Overall']['Average_Correlation']:.4f}
        - 5%目标达成: {'✅ 是' if metrics['Overall']['Target_Achievement'] else '❌ 否'}

        📈 各变量详细指标:
        """

        for var in ['S', 'Pz', 'Yc']:
            var_metrics = metrics[var]
            report += f"""
        {var} 变量:
        - MAPE: {var_metrics['MAPE']:.2f}%
        - R²: {var_metrics['R2']:.4f}
        - 相关系数: {var_metrics['Correlation']:.4f}
        - RMSE: {var_metrics['RMSE']:.4f}
        - 最大误差: {var_metrics['Max_Error']:.4f}
        - 中位数误差: {var_metrics['Q50_Error']:.4f}
        """

        # 性能等级评估
        overall_mape = metrics['Overall']['Average_MAPE']
        if overall_mape <= 5.0:
            performance_grade = "🏆 优秀 (≤5%)"
        elif overall_mape <= 10.0:
            performance_grade = "🥈 良好 (5-10%)"
        elif overall_mape <= 15.0:
            performance_grade = "🥉 一般 (10-15%)"
        else:
            performance_grade = "❌ 需要改进 (>15%)"

        report += f"""

        🎯 性能等级: {performance_grade}

        💡 改进建议:
        """

        # 生成改进建议
        suggestions = self.generate_improvement_suggestions(metrics)
        for suggestion in suggestions:
            report += f"- {suggestion}\n"

        return report

    def generate_improvement_suggestions(self, metrics):
        """
        生成改进建议
        """
        suggestions = []

        # 基于各变量性能给出建议
        for var in ['S', 'Pz', 'Yc']:
            mape = metrics[var]['MAPE']
            r2 = metrics[var]['R2']

            if mape > 10.0:
                suggestions.append(f"{var}变量精度较低({mape:.1f}%)，建议增加该变量的损失权重")

            if r2 < 0.8:
                suggestions.append(f"{var}变量拟合度较低(R²={r2:.3f})，建议检查特征工程")

        # 整体建议
        overall_mape = metrics['Overall']['Average_MAPE']
        if overall_mape > 5.0:
            suggestions.append("整体精度未达到5%目标，建议:")
            suggestions.append("  1. 增加训练数据量")
            suggestions.append("  2. 调整模型架构")
            suggestions.append("  3. 优化超参数")
            suggestions.append("  4. 考虑集成学习")

        return suggestions if suggestions else ["模型性能良好，无需特别改进"]

    def compare_models(self, model_results=None):
        """
        比较多个模型的性能
        """
        if model_results is None:
            model_results = self.results_history

        if len(model_results) < 2:
            print("需要至少2个模型结果进行比较")
            return

        print("\n" + "="*60)
        print("🏆 模型性能对比")
        print("="*60)

        # 创建对比表格
        comparison_data = []
        for result in model_results:
            model_name = result['model_name']
            metrics = result['metrics']

            row = {
                'Model': model_name,
                'Overall_MAPE': f"{metrics['Overall']['Average_MAPE']:.2f}%",
                'S_MAPE': f"{metrics['S']['MAPE']:.2f}%",
                'Pz_MAPE': f"{metrics['Pz']['MAPE']:.2f}%",
                'Yc_MAPE': f"{metrics['Yc']['MAPE']:.2f}%",
                'Avg_R2': f"{metrics['Overall']['Average_R2']:.4f}",
                'Target_Met': "✅" if metrics['Overall']['Target_Achievement'] else "❌"
            }
            comparison_data.append(row)

        # 按整体MAPE排序
        comparison_data.sort(key=lambda x: float(x['Overall_MAPE'].rstrip('%')))

        # 打印对比表格
        headers = ['Model', 'Overall_MAPE', 'S_MAPE', 'Pz_MAPE', 'Yc_MAPE', 'Avg_R2', 'Target_Met']

        # 计算列宽
        col_widths = {}
        for header in headers:
            col_widths[header] = max(len(header), max(len(str(row[header])) for row in comparison_data))

        # 打印表头
        header_row = " | ".join(header.ljust(col_widths[header]) for header in headers)
        print(header_row)
        print("-" * len(header_row))

        # 打印数据行
        for row in comparison_data:
            data_row = " | ".join(str(row[header]).ljust(col_widths[header]) for header in headers)
            print(data_row)

        # 找出最佳模型
        best_model = comparison_data[0]
        print(f"\n🥇 最佳模型: {best_model['Model']} (MAPE: {best_model['Overall_MAPE']})")

        # 生成对比分析
        self.generate_comparison_analysis(model_results)

        return comparison_data

    def generate_comparison_analysis(self, model_results):
        """
        生成对比分析
        """
        print("\n📊 详细对比分析:")

        # 各变量最佳模型
        best_models = {'S': None, 'Pz': None, 'Yc': None}
        best_scores = {'S': float('inf'), 'Pz': float('inf'), 'Yc': float('inf')}

        for result in model_results:
            model_name = result['model_name']
            metrics = result['metrics']

            for var in ['S', 'Pz', 'Yc']:
                mape = metrics[var]['MAPE']
                if mape < best_scores[var]:
                    best_scores[var] = mape
                    best_models[var] = model_name

        print("\n各变量最佳模型:")
        for var in ['S', 'Pz', 'Yc']:
            print(f"- {var}: {best_models[var]} ({best_scores[var]:.2f}%)")

        # 性能分布分析
        all_mapes = [result['metrics']['Overall']['Average_MAPE'] for result in model_results]
        print(f"\n性能分布:")
        print(f"- 最佳: {min(all_mapes):.2f}%")
        print(f"- 最差: {max(all_mapes):.2f}%")
        print(f"- 平均: {sum(all_mapes)/len(all_mapes):.2f}%")
        print(f"- 标准差: {torch.std(torch.tensor(all_mapes)).item():.2f}%")

        # 目标达成情况
        target_met_count = sum(1 for result in model_results
                              if result['metrics']['Overall']['Target_Achievement'])
        print(f"\n🎯 目标达成情况: {target_met_count}/{len(model_results)} 个模型达到5%目标")

    def export_results(self, filename="model_evaluation_results.json"):
        """
        导出评估结果
        """
        import json

        # 准备导出数据 (移除不能序列化的tensor)
        export_data = []
        for result in self.results_history:
            export_result = {
                'model_name': result['model_name'],
                'metrics': result['metrics'],
                'report': result['report']
            }
            export_data.append(export_result)

        # 保存到文件
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)

        print(f"✅ 评估结果已导出到: {filename}")

    def create_performance_summary(self):
        """
        创建性能摘要
        """
        if not self.results_history:
            print("没有可用的评估结果")
            return

        print("\n" + "="*50)
        print("📋 性能摘要")
        print("="*50)

        for result in self.results_history:
            model_name = result['model_name']
            metrics = result['metrics']
            overall_mape = metrics['Overall']['Average_MAPE']
            target_met = metrics['Overall']['Target_Achievement']

            status = "🎯 达标" if target_met else "⚠️  未达标"
            print(f"{model_name}: {overall_mape:.2f}% {status}")

        # 推荐最佳模型
        best_result = min(self.results_history,
                         key=lambda x: x['metrics']['Overall']['Average_MAPE'])

        print(f"\n💡 推荐使用: {best_result['model_name']}")
        print(f"   性能: {best_result['metrics']['Overall']['Average_MAPE']:.2f}% MAPE")

        return best_result

# ============================
# 最终集成训练和评估系统
# ============================

class FinalOptimizedTrainingSystem:
    """
    最终优化的训练和评估系统

    集成所有改进功能:
    1. 增强物理模型
    2. 自适应权重系统
    3. 现代训练算法
    4. 综合测试框架
    5. 5%精度目标优化
    """

    def __init__(self, device='cuda', target_accuracy=0.05):
        self.device = device
        self.target_accuracy = target_accuracy

        # 初始化各个组件
        self.transfer_trainer = OptimizedTransferLearningTrainer(device, target_accuracy)
        self.weight_controller = AdaptiveWeightController(target_accuracy=target_accuracy)
        self.testing_framework = ComprehensiveTestingFramework(device)
        self.training_techniques = None  # 将在训练时初始化

        # 训练历史
        self.training_history = []

    def create_enhanced_model(self, use_physics=True):
        """
        创建增强版模型
        """
        model = StreamlinedTransferPredictor(
            input_dim=2,  # 迁移学习使用2D输入
            hidden_dim=320,
            num_layers=4,
            dropout=0.12,
            seq_len=100,
            use_physics=use_physics,
            physics_weight=0.1
        ).to(self.device)

        return model

    def train_enhanced_finalbest(self, train_loader, val_loader, test_loader):
        """
        训练增强版FinalBest模型
        """
        print("🚀 开始训练增强版EnhancedFinalBest模型")
        print("="*60)

        # 创建模型
        model = self.create_enhanced_model(use_physics=True)
        self.training_techniques = AdvancedTrainingTechniques(model, self.device)

        # 设置EMA
        self.training_techniques.setup_ema()

        # 创建高级优化器和调度器
        optimizer = ModernTrainingAlgorithms.create_advanced_optimizer(
            model, optimizer_type='adamw', lr=1e-3, weight_decay=1e-4
        )

        scheduler = ModernTrainingAlgorithms.create_advanced_scheduler(
            optimizer, scheduler_type='cosine_warmup', epochs=200, warmup_epochs=20
        )

        # 训练配置
        epochs = 200
        patience = 30
        best_val_loss = float('inf')
        patience_counter = 0

        print(f"📋 训练配置:")
        print(f"- 目标精度: {self.target_accuracy*100}%")
        print(f"- 最大轮数: {epochs}")
        print(f"- 早停耐心: {patience}")
        print(f"- 设备: {self.device}")

        for epoch in range(epochs):
            # 训练阶段
            model.train()
            train_loss = 0
            num_batches = 0

            for batch_idx, (data, targets) in enumerate(train_loader):
                # 使用2D输入 (质量, 高度)
                train_data = data[:, :, :2].to(self.device)
                targets = targets.to(self.device)

                # 数据增强 (可选)
                if torch.rand(1).item() < 0.3:  # 30%概率应用Mixup
                    train_data, targets = self.training_techniques.apply_mixup(
                        train_data, targets, alpha=0.2
                    )

                # 前向传播
                optimizer.zero_grad()
                outputs = model(train_data, epoch_ratio=epoch/epochs)

                # 计算损失
                loss, loss_dict = model.compute_multitask_loss(
                    outputs, targets, epoch_ratio=epoch/epochs
                )

                # 反向传播
                loss.backward()
                ModernTrainingAlgorithms.apply_gradient_clipping(model, 'norm', 1.0)
                optimizer.step()

                # 更新EMA
                self.training_techniques.update_ema()

                train_loss += loss.item()
                num_batches += 1

            # 学习率调度
            scheduler.step()

            # 验证阶段
            val_loss = self.validate_model(model, val_loader)
            avg_train_loss = train_loss / num_batches

            # 自适应权重更新
            val_accuracy = self.quick_accuracy_check(model, val_loader)
            weight_info = self.weight_controller.update_weights(
                model, val_loss, val_accuracy, epoch/epochs
            )

            # 记录训练历史
            epoch_info = {
                'epoch': epoch + 1,
                'train_loss': avg_train_loss,
                'val_loss': val_loss,
                'val_accuracy': val_accuracy,
                'lr': optimizer.param_groups[0]['lr'],
                'physics_weight': weight_info['physics_weight']
            }
            self.training_history.append(epoch_info)

            # 打印进度
            if (epoch + 1) % 10 == 0:
                print(f"Epoch {epoch+1:3d}/{epochs}: "
                      f"Train Loss: {avg_train_loss:.6f}, "
                      f"Val Loss: {val_loss:.6f}, "
                      f"Val Acc: {val_accuracy:.2f}%, "
                      f"LR: {optimizer.param_groups[0]['lr']:.2e}")

            # 早停检查
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # 保存最佳模型
                torch.save(model.state_dict(), 'best_enhanced_finalbest.pth')

                # 如果达到目标精度，可以提前结束
                if val_accuracy <= self.target_accuracy * 100:
                    print(f"🎯 达到目标精度 {val_accuracy:.2f}% <= {self.target_accuracy*100}%!")
                    break
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    print(f"⏰ 早停在epoch {epoch+1}")
                    break

        # 加载最佳模型
        model.load_state_dict(torch.load('best_enhanced_finalbest.pth'))

        # 最终评估
        print("\n🔍 最终评估:")
        final_result = self.testing_framework.evaluate_model_comprehensive(
            model, test_loader, "EnhancedFinalBest"
        )

        print(final_result['report'])

        return model, final_result

    def train_baseline_models(self, train_loader, val_loader, test_loader):
        """
        训练基线模型进行对比
        """
        print("\n📊 训练基线模型进行对比")
        print("="*60)

        baseline_results = []

        # 1. 训练简化的CNN模型
        print("🔧 训练CNN基线模型...")
        cnn_model = CNNPredictor(input_dim=2).to(self.device)
        cnn_result = self.train_baseline_model(
            cnn_model, train_loader, val_loader, test_loader, "CNN"
        )
        baseline_results.append(cnn_result)

        # 2. 训练LSTM模型
        print("🔧 训练LSTM基线模型...")
        lstm_model = LSTMPredictor(input_dim=2, hidden_dim=320).to(self.device)
        lstm_result = self.train_baseline_model(
            lstm_model, train_loader, val_loader, test_loader, "LSTM"
        )
        baseline_results.append(lstm_result)

        # 3. 训练Transformer模型
        print("🔧 训练Transformer基线模型...")
        transformer_model = EnhancedTransformerPredictor(
            input_dim=2, d_model=320, nhead=8, num_layers=4
        ).to(self.device)
        transformer_result = self.train_baseline_model(
            transformer_model, train_loader, val_loader, test_loader, "Transformer"
        )
        baseline_results.append(transformer_result)

        return baseline_results

    def train_baseline_model(self, model, train_loader, val_loader, test_loader, model_name):
        """
        训练单个基线模型
        """
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3, weight_decay=1e-4)
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=100)

        best_val_loss = float('inf')
        patience_counter = 0
        patience = 20

        for epoch in range(100):  # 基线模型训练轮数较少
            model.train()
            train_loss = 0
            num_batches = 0

            for data, targets in train_loader:
                train_data = data[:, :, :2].to(self.device)
                targets = targets.to(self.device)

                optimizer.zero_grad()
                outputs = model(train_data)
                loss, _ = model.compute_multitask_loss(outputs, targets)

                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                optimizer.step()

                train_loss += loss.item()
                num_batches += 1

            scheduler.step()
            val_loss = self.validate_model(model, val_loader)

            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                torch.save(model.state_dict(), f'best_{model_name.lower()}.pth')
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    break

        # 加载最佳模型并评估
        model.load_state_dict(torch.load(f'best_{model_name.lower()}.pth'))
        result = self.testing_framework.evaluate_model_comprehensive(
            model, test_loader, model_name
        )

        return result

    def validate_model(self, model, val_loader):
        """验证模型"""
        model.eval()
        total_loss = 0
        num_batches = 0

        with torch.no_grad():
            for data, targets in val_loader:
                val_data = data[:, :, :2].to(self.device)
                targets = targets.to(self.device)

                outputs = model(val_data)
                loss, _ = model.compute_multitask_loss(outputs, targets)

                total_loss += loss.item()
                num_batches += 1

        return total_loss / num_batches

    def quick_accuracy_check(self, model, val_loader):
        """快速精度检查"""
        model.eval()
        total_error = 0
        num_samples = 0

        with torch.no_grad():
            for i, (data, targets) in enumerate(val_loader):
                if i >= 3:  # 只检查前几个批次
                    break

                val_data = data[:, :, :2].to(self.device)
                targets = targets.to(self.device)

                outputs = model(val_data)
                predictions = outputs['predictions']

                # 计算MAPE
                error = torch.mean(torch.abs((predictions - targets) / (targets + 1e-8)))
                total_error += error.item()
                num_samples += 1

        return (total_error / num_samples * 100) if num_samples > 0 else 100.0

    def run_complete_evaluation(self, train_loader, val_loader, test_loader):
        """
        运行完整的评估流程
        """
        print("🎯 开始完整的模型训练和评估流程")
        print("="*80)

        # 1. 训练增强版FinalBest模型
        enhanced_model, enhanced_result = self.train_enhanced_finalbest(
            train_loader, val_loader, test_loader
        )

        # 2. 训练基线模型
        baseline_results = self.train_baseline_models(
            train_loader, val_loader, test_loader
        )

        # 3. 模型对比
        all_results = [enhanced_result] + baseline_results
        comparison_data = self.testing_framework.compare_models(all_results)

        # 4. 生成最终报告
        self.generate_final_report(enhanced_result, baseline_results, comparison_data)

        # 5. 导出结果
        self.testing_framework.export_results("final_evaluation_results.json")

        return enhanced_model, enhanced_result, baseline_results

    def generate_final_report(self, enhanced_result, baseline_results, comparison_data):
        """
        生成最终报告
        """
        print("\n" + "="*80)
        print("📋 最终评估报告")
        print("="*80)

        enhanced_mape = enhanced_result['metrics']['Overall']['Average_MAPE']
        enhanced_target_met = enhanced_result['metrics']['Overall']['Target_Achievement']

        print(f"\n🏆 EnhancedFinalBest 性能:")
        print(f"- 平均MAPE: {enhanced_mape:.2f}%")
        print(f"- 5%目标达成: {'✅ 是' if enhanced_target_met else '❌ 否'}")

        # 与基线模型对比
        print(f"\n📊 与基线模型对比:")
        for baseline_result in baseline_results:
            baseline_name = baseline_result['model_name']
            baseline_mape = baseline_result['metrics']['Overall']['Average_MAPE']
            improvement = baseline_mape - enhanced_mape

            print(f"- vs {baseline_name}: {improvement:+.2f}% "
                  f"({'✅ 更好' if improvement > 0 else '❌ 更差'})")

        # 各变量性能
        print(f"\n📈 各变量详细性能:")
        for var in ['S', 'Pz', 'Yc']:
            var_mape = enhanced_result['metrics'][var]['MAPE']
            var_r2 = enhanced_result['metrics'][var]['R2']
            print(f"- {var}: MAPE={var_mape:.2f}%, R²={var_r2:.4f}")

        # 训练历史摘要
        if self.training_history:
            final_epoch = self.training_history[-1]
            print(f"\n📚 训练摘要:")
            print(f"- 训练轮数: {final_epoch['epoch']}")
            print(f"- 最终学习率: {final_epoch['lr']:.2e}")
            print(f"- 最终物理权重: {final_epoch['physics_weight']:.4f}")

        # 结论
        print(f"\n🎯 结论:")
        if enhanced_target_met:
            print("✅ EnhancedFinalBest模型成功达到5%精度目标!")
            print("✅ 模型已准备好用于实际应用")
        else:
            print("⚠️  EnhancedFinalBest模型未达到5%精度目标")
            print("💡 建议进一步优化或收集更多训练数据")

        # 推荐使用的模型
        best_model = min([enhanced_result] + baseline_results,
                        key=lambda x: x['metrics']['Overall']['Average_MAPE'])

        print(f"\n💡 推荐使用模型: {best_model['model_name']}")
        print(f"   性能: {best_model['metrics']['Overall']['Average_MAPE']:.2f}% MAPE")

    def get_training_summary(self):
        """
        获取训练摘要
        """
        if not self.training_history:
            return "没有训练历史记录"

        summary = {
            'total_epochs': len(self.training_history),
            'best_val_loss': min(epoch['val_loss'] for epoch in self.training_history),
            'best_val_accuracy': min(epoch['val_accuracy'] for epoch in self.training_history),
            'final_lr': self.training_history[-1]['lr'],
            'final_physics_weight': self.training_history[-1]['physics_weight'],
            'target_achieved': min(epoch['val_accuracy'] for epoch in self.training_history) <= self.target_accuracy * 100
        }

        return summary

# 创建全局实例以便使用
final_training_system = FinalOptimizedTrainingSystem()

class OursPredictor(nn.Module):
    """优化版最先进的落锤试验预测模型 - 借鉴LSTM成功经验的简化架构"""
    def __init__(self, input_dim=3, d_model=256, nhead=8, num_layers=3,
                 seq_len=501, dropout=0.1):
        super(OursPredictor, self).__init__()

        self.input_dim = input_dim
        self.d_model = d_model
        self.seq_len = seq_len

        # 1. 借鉴LSTM的简洁输入处理 - 移除复杂的PixelCNN
        self.input_projection = nn.Sequential(
            nn.Linear(input_dim, d_model//2),
            nn.LayerNorm(d_model//2),
            nn.GELU(),
            nn.Dropout(dropout * 0.3),  # 适度dropout
            nn.Linear(d_model//2, d_model),
            nn.LayerNorm(d_model),
            nn.GELU(),
            nn.Dropout(dropout * 0.3)
        )

        # 2. 核心LSTM层 - 借鉴LSTM的成功架构
        self.lstm = nn.LSTM(
            d_model, d_model//2,
            num_layers=4,  # 与成功的LSTM保持一致
            bidirectional=True,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0
        )

        # 3. 简化的注意力机制 - 保持关键特征
        self.attention = nn.MultiheadAttention(
            d_model, num_heads=8, dropout=dropout * 0.5, batch_first=True
        )

        # 4. 起落架物理约束模块 - 保持物理约束优势
        self.physics_module = LandingGearPhysicsModule(hidden_dim=d_model//2)

        # 5. 简化的输出层 - 借鉴LSTM的统一输出设计
        self.output_head = nn.Sequential(
            nn.Linear(d_model, d_model//2),
            nn.LayerNorm(d_model//2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model//2, d_model//4),
            nn.GELU(),
            nn.Linear(d_model//4, 3)  # [S, Pz, Yc] 统一输出
        )

        # 6. 优化的物理约束融合权重 - 基于LSTM经验调整
        self.physics_weight = nn.Parameter(torch.tensor(0.1))  # 适度物理约束

        # 7. 重新调整的损失权重 - 重点优化S和Yc
        self.loss_weights = {
            'S': 3.0,      # 大幅提高S权重
            'Pz': 1.0,     # Pz表现较好，保持基准
            'Yc': 3.0,     # 大幅提高Yc权重
            'physics': 0.2 # 适度物理约束
        }
        
    def forward(self, x, height_info=None, mass_info=None, epoch_ratio=0.5):
        batch_size, seq_len, input_dim = x.shape

        # 从输入中提取质量、高度信息
        mass_norm = x[:, 0, 0]    # 取第一个时间步的质量
        height_norm = x[:, 0, 1]  # 取第一个时间步的高度

        # 反归一化得到实际值
        mass = mass_norm * 3000.0      # kg
        height = height_norm * 3000.0  # mm

        # 1. 物理约束预测 - 基于质量和高度
        Pz_physics, S_physics, Yc_physics = self.physics_module(height, mass)

        # 2. 借鉴LSTM的简洁输入处理
        x_projected = self.input_projection(x)  # [B, L, d_model]

        # 3. LSTM处理 - 核心特征提取
        lstm_out, _ = self.lstm(x_projected)  # [B, L, d_model]

        # 4. 注意力增强
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)  # [B, L, d_model]

        # 5. 残差连接 - 借鉴LSTM的稳定训练经验
        enhanced_features = lstm_out + attn_out

        # 6. 统一预测 - 借鉴LSTM的简洁输出设计
        nn_predictions = self.output_head(enhanced_features)  # [B, L, 3]
        S_nn = nn_predictions[..., 0]
        Pz_nn = nn_predictions[..., 1]
        Yc_nn = nn_predictions[..., 2]

        # 7. 物理约束与神经网络融合 - 简化融合策略
        # 归一化物理约束输出
        pz_min, pz_max = -1.630496, 203.920620
        s_min, s_max = -536.617130, 45.976616

        S_physics_norm = (S_physics - s_min) / (s_max - s_min + 1e-8)
        Pz_physics_norm = (Pz_physics - pz_min) / (pz_max - pz_min + 1e-8)
        Yc_physics_norm = torch.clamp(Yc_physics / 100.0, 0, 1)

        # 扩展到序列长度
        S_physics_expanded = S_physics_norm.unsqueeze(1).expand(-1, seq_len)
        Pz_physics_expanded = Pz_physics_norm.unsqueeze(1).expand(-1, seq_len)
        Yc_physics_expanded = Yc_physics_norm.unsqueeze(1).expand(-1, seq_len)

        # 简化的融合策略
        alpha = torch.sigmoid(self.physics_weight)

        S_pred = alpha * S_physics_expanded + (1 - alpha) * S_nn
        Pz_pred = alpha * Pz_physics_expanded + (1 - alpha) * Pz_nn
        Yc_pred = alpha * Yc_physics_expanded + (1 - alpha) * Yc_nn

        outputs = {
            'predictions': torch.stack([S_pred, Pz_pred, Yc_pred], dim=-1),
            'S': S_pred,
            'Pz': Pz_pred,
            'Yc': Yc_pred,
            'features': enhanced_features,
            'physics_info': {'Pz_physics': Pz_physics, 'S_physics': S_physics, 'Yc_physics': Yc_physics},
            'Pz_physics': Pz_physics,
            'Pz_nn': Pz_nn,
            'Yc_physics': Yc_physics,
            'Yc_nn': Yc_nn,
            'S_physics': S_physics,
            'S_nn': S_nn
        }

        return outputs
    
    def compute_multitask_loss(self, outputs, targets, epoch_ratio=0.5, input_data=None, yc_target=None):
        """优化的多任务损失计算 - 重点优化S和Yc，借鉴LSTM成功经验"""
        S_target, Pz_target, Yc_target = targets[..., 0], targets[..., 1], targets[..., 2]

        # 1. 主要预测损失 - 使用Smooth L1损失提高稳定性
        loss_S = F.smooth_l1_loss(outputs['S'], S_target)
        loss_Pz = F.smooth_l1_loss(outputs['Pz'], Pz_target)
        loss_Yc = F.smooth_l1_loss(outputs['Yc'], Yc_target)

        # 2. 物理一致性损失 - 简化版本
        loss_physics = torch.tensor(0.0, device=targets.device)
        if 'Pz_physics' in outputs and 'Pz_nn' in outputs:
            Pz_physics_expanded = outputs['Pz_physics'].unsqueeze(1).expand(-1, outputs['Pz_nn'].size(1))
            loss_physics = F.mse_loss(outputs['Pz_nn'], Pz_physics_expanded.detach())

        # 3. 动态权重策略 - 重点优化S和Yc
        # 基于训练进度动态调整权重
        progress_factor = min(1.0, epoch_ratio * 2.0)  # 训练进度因子

        # S和Yc权重随训练进度增加（重点优化）
        s_weight = self.loss_weights['S'] * (1.0 + 2.0 * progress_factor)  # 3.0 -> 9.0
        yc_weight = self.loss_weights['Yc'] * (1.0 + 2.0 * progress_factor)  # 3.0 -> 9.0
        pz_weight = self.loss_weights['Pz']  # Pz保持基准权重
        physics_weight = self.loss_weights['physics'] * (1.0 - 0.5 * progress_factor)  # 物理权重逐渐降低

        # 4. 组合损失 - 重点关注S和Yc
        total_loss = (
            s_weight * loss_S +
            pz_weight * loss_Pz +
            yc_weight * loss_Yc +
            physics_weight * loss_physics
        )

        return total_loss, {
            'loss_S': loss_S.item(),
            'loss_Pz': loss_Pz.item(),
            'loss_Yc': loss_Yc.item(),
            'loss_physics': loss_physics.item(),
            'total_loss': total_loss.item(),
            's_weight': s_weight,
            'yc_weight': yc_weight,
            'pz_weight': pz_weight,
            'physics_weight': physics_weight
        }

class PositionalEncoding(nn.Module):
    """改进的位置编码"""
    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        self.register_buffer('pe', pe.unsqueeze(0))
        
    def forward(self, x):
        return x + self.pe[:, :x.size(1)]

# ============================
# 改进的基线模型
# ============================

class CNNPredictor(nn.Module):
    """CNN预测器 - 用于算法对比"""
    def __init__(self, input_dim=20):
        super(CNNPredictor, self).__init__()
        
        # 多尺度卷积
        self.multi_scale_conv = nn.ModuleList([
            nn.Conv1d(input_dim, 64, kernel_size=3, padding=1),
            nn.Conv1d(input_dim, 64, kernel_size=5, padding=2),
            nn.Conv1d(input_dim, 64, kernel_size=7, padding=3),
        ])
        
        # 深度卷积网络
        self.conv_blocks = nn.Sequential(
            # Block 1
            nn.Conv1d(192, 128, kernel_size=3, padding=1),
            nn.BatchNorm1d(128),
            nn.GELU(),
            nn.Dropout(0.1),
            
            # Block 2
            nn.Conv1d(128, 256, kernel_size=3, padding=1),
            nn.BatchNorm1d(256),
            nn.GELU(),
            nn.Dropout(0.1),
            
            # Block 3
            nn.Conv1d(256, 512, kernel_size=3, padding=1),
            nn.BatchNorm1d(512),
            nn.GELU(),
            nn.Dropout(0.1),
            
            # Block 4
            nn.Conv1d(512, 256, kernel_size=3, padding=1),
            nn.BatchNorm1d(256),
            nn.GELU(),
        )
        
        # S和Pz输出层
        self.output_layer = nn.Sequential(
            nn.Conv1d(256, 128, kernel_size=1),
            nn.GELU(),
            nn.Conv1d(128, 2, kernel_size=1)
        )

        # Yc预测头 - 使用全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        self.yc_head = nn.Sequential(
            nn.Linear(256, 128),
            nn.LayerNorm(128),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(128, 64),
            nn.GELU(),
            nn.Linear(64, 1)
        )
        
    def forward(self, x, height_info=None, mass_info=None):
        batch_size, seq_len, _ = x.shape

        x = x.transpose(1, 2)  # [B, C, L]

        # 多尺度特征提取
        multi_scale_features = []
        for conv in self.multi_scale_conv:
            multi_scale_features.append(conv(x))

        # 特征拼接
        x = torch.cat(multi_scale_features, dim=1)

        # 深度卷积处理
        conv_features = self.conv_blocks(x)

        # S和Pz预测
        sp_output = self.output_layer(conv_features)
        sp_output = sp_output.transpose(1, 2)  # [B, L, 2]

        # Yc预测 - 使用全局平均池化
        global_features = self.global_pool(conv_features).squeeze(-1)  # [B, 256]

        # 如果有高度和质量信息，融入特征
        if height_info is not None and mass_info is not None:
            height_tensor = torch.tensor(height_info / 200.0, device=x.device, dtype=x.dtype).view(-1, 1)
            mass_tensor = torch.tensor(mass_info / 700.0, device=x.device, dtype=x.dtype).view(-1, 1)

            # 扩展物理信息
            physics_info = torch.cat([height_tensor, mass_tensor], dim=-1)  # [B, 2]
            physics_expanded = physics_info.unsqueeze(1).expand(-1, global_features.size(1) // 2, -1).reshape(batch_size, -1)

            # 融合特征
            if physics_expanded.size(1) <= global_features.size(1):
                global_features[:, :physics_expanded.size(1)] += physics_expanded

        yc_raw = self.yc_head(global_features)  # [B, 1]
        yc_pred = 45 + torch.sigmoid(yc_raw) * 20  # 缩放到[45, 65] MPa，更小范围提高精度
        yc_pred = yc_pred.squeeze(-1).unsqueeze(1).expand(-1, seq_len)  # [B, L]

        return {
            'predictions': sp_output,
            'S': sp_output[..., 0:1].squeeze(-1),
            'Pz': sp_output[..., 1:2].squeeze(-1),
            'Yc': yc_pred.squeeze(-1)
        }

    def compute_multitask_loss(self, outputs, targets, epoch_ratio=0.5, input_data=None, yc_target=None):
        """CNN模型的多任务损失计算"""
        S_target, Pz_target, Yc_target = targets[..., 0], targets[..., 1], targets[..., 2]

        # 主要预测损失
        loss_S = F.smooth_l1_loss(outputs['S'], S_target)
        loss_Pz = F.smooth_l1_loss(outputs['Pz'], Pz_target)
        loss_Yc = F.smooth_l1_loss(outputs['Yc'], Yc_target)

        # 简化的损失权重
        total_loss = 2.0 * loss_S + 1.5 * loss_Pz + 3.0 * loss_Yc

        return total_loss, {
            'loss_S': loss_S.item(),
            'loss_Pz': loss_Pz.item(),
            'loss_Yc': loss_Yc.item(),
            'loss_recon': 0.0,
            'loss_kl': 0.0,
            'loss_physics': 0.0,
            'total_loss': total_loss.item(),
            'physics_weight': 0.0,
            'data_weight': 1.0
        }

class LSTMPredictor(nn.Module):
    """LSTM预测器 - 用于算法对比"""
    def __init__(self, input_dim=20, hidden_dim=512):
        super(LSTMPredictor, self).__init__()
        
        # 输入投影
        self.input_proj = nn.Linear(input_dim, hidden_dim)
        
        # 深度双向LSTM
        self.lstm = nn.LSTM(
            hidden_dim, hidden_dim,
            num_layers=6,
            bidirectional=True,
            batch_first=True,
            dropout=0.1
        )
        
        # 自注意力
        self.attention = nn.MultiheadAttention(
            hidden_dim * 2, num_heads=8, dropout=0.1, batch_first=True
        )
        
        # S和Pz输出网络
        self.output_net = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.GELU(),
            nn.Linear(hidden_dim // 2, 2)
        )

        # Yc预测头
        self.yc_head = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.GELU(),
            nn.Linear(hidden_dim // 4, 1)
        )
        
    def forward(self, x, height_info=None, mass_info=None):
        batch_size, seq_len, _ = x.shape

        # 输入投影
        x = self.input_proj(x)

        # LSTM处理
        lstm_out, _ = self.lstm(x)

        # 自注意力增强
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)

        # 残差连接
        combined = lstm_out + attn_out

        # S和Pz预测
        sp_output = self.output_net(combined)

        # Yc预测 - 使用全局平均池化
        global_features = torch.mean(combined, dim=1)  # [B, hidden_dim*2]

        # 如果有高度和质量信息，融入特征
        if height_info is not None and mass_info is not None:
            height_tensor = torch.tensor(height_info / 200.0, device=x.device, dtype=x.dtype).view(-1, 1)
            mass_tensor = torch.tensor(mass_info / 700.0, device=x.device, dtype=x.dtype).view(-1, 1)

            # 扩展物理信息
            physics_info = torch.cat([height_tensor, mass_tensor], dim=-1)  # [B, 2]
            physics_expanded = physics_info.unsqueeze(1).expand(-1, global_features.size(1) // 2, -1).reshape(batch_size, -1)

            # 融合特征
            if physics_expanded.size(1) <= global_features.size(1):
                global_features[:, :physics_expanded.size(1)] += physics_expanded

        yc_raw = self.yc_head(global_features)  # [B, 1]
        yc_pred = 45 + torch.sigmoid(yc_raw) * 20  # 缩放到[45, 65] MPa，更小范围提高精度
        yc_pred = yc_pred.squeeze(-1).unsqueeze(1).expand(-1, seq_len)  # [B, L]

        return {
            'predictions': sp_output,
            'S': sp_output[..., 0:1].squeeze(-1),
            'Pz': sp_output[..., 1:2].squeeze(-1),
            'Yc': yc_pred.squeeze(-1)
        }

    def compute_multitask_loss(self, outputs, targets, epoch_ratio=0.5, input_data=None, yc_target=None):
        """LSTM模型的多任务损失计算"""
        S_target, Pz_target, Yc_target = targets[..., 0], targets[..., 1], targets[..., 2]

        # 主要预测损失
        loss_S = F.smooth_l1_loss(outputs['S'], S_target)
        loss_Pz = F.smooth_l1_loss(outputs['Pz'], Pz_target)
        loss_Yc = F.smooth_l1_loss(outputs['Yc'], Yc_target)

        # 简化的损失权重
        total_loss = 2.0 * loss_S + 1.5 * loss_Pz + 3.0 * loss_Yc

        return total_loss, {
            'loss_S': loss_S.item(),
            'loss_Pz': loss_Pz.item(),
            'loss_Yc': loss_Yc.item(),
            'loss_recon': 0.0,
            'loss_kl': 0.0,
            'loss_physics': 0.0,
            'total_loss': total_loss.item(),
            'physics_weight': 0.0,
            'data_weight': 1.0
        }

class CNNLSTMPredictor(nn.Module):
    """CNN-LSTM混合预测器 - 用于算法对比"""
    def __init__(self, input_dim=20, hidden_dim=512):
        super(CNNLSTMPredictor, self).__init__()
        
        # CNN特征提取器
        self.cnn_extractor = nn.Sequential(
            # 第一层
            nn.Conv1d(input_dim, 128, kernel_size=5, padding=2),
            nn.BatchNorm1d(128),
            nn.GELU(),

            # 第二层
            nn.Conv1d(128, 256, kernel_size=3, padding=1),
            nn.BatchNorm1d(256),
            nn.GELU(),

            # 第三层
            nn.Conv1d(256, 512, kernel_size=3, padding=1),
            nn.BatchNorm1d(512),
            nn.GELU(),
        )
        
        # 时序调整层
        self.temporal_adjust = nn.Sequential(
            nn.Conv1d(512, hidden_dim, kernel_size=1),
            nn.GELU()
        )
        
        # LSTM层
        self.lstm = nn.LSTM(
            hidden_dim, hidden_dim,
            num_layers=4,
            bidirectional=True,
            batch_first=True,
            dropout=0.1
        )
        
        # S和Pz输出层
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, 2)
        )

        # Yc预测头
        self.yc_head = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.GELU(),
            nn.Linear(hidden_dim // 4, 1)
        )
        
    def forward(self, x, height_info=None, mass_info=None):
        batch_size, seq_len, _ = x.shape

        # CNN特征提取
        x_cnn = x.transpose(1, 2)  # [B, C, L]
        cnn_features = self.cnn_extractor(x_cnn)

        # 调整维度
        cnn_features = self.temporal_adjust(cnn_features)
        cnn_features = cnn_features.transpose(1, 2)  # [B, L, C]

        # LSTM处理
        lstm_out, _ = self.lstm(cnn_features)

        # S和Pz预测
        sp_output = self.output_layer(lstm_out)

        # Yc预测 - 使用全局平均池化
        global_features = torch.mean(lstm_out, dim=1)  # [B, hidden_dim*2]

        # 如果有高度和质量信息，融入特征
        if height_info is not None and mass_info is not None:
            height_tensor = torch.tensor(height_info / 200.0, device=x.device, dtype=x.dtype).view(-1, 1)
            mass_tensor = torch.tensor(mass_info / 700.0, device=x.device, dtype=x.dtype).view(-1, 1)

            # 扩展物理信息
            physics_info = torch.cat([height_tensor, mass_tensor], dim=-1)  # [B, 2]
            physics_expanded = physics_info.unsqueeze(1).expand(-1, global_features.size(1) // 2, -1).reshape(batch_size, -1)

            # 融合特征
            if physics_expanded.size(1) <= global_features.size(1):
                global_features[:, :physics_expanded.size(1)] += physics_expanded

        yc_raw = self.yc_head(global_features)  # [B, 1]
        yc_pred = 45 + torch.sigmoid(yc_raw) * 20  # 缩放到[45, 65] MPa，更小范围提高精度
        yc_pred = yc_pred.squeeze(-1).unsqueeze(1).expand(-1, seq_len)  # [B, L]

        return {
            'predictions': sp_output,
            'S': sp_output[..., 0:1].squeeze(-1),
            'Pz': sp_output[..., 1:2].squeeze(-1),
            'Yc': yc_pred.squeeze(-1)
        }

    def compute_multitask_loss(self, outputs, targets, epoch_ratio=0.5, input_data=None, yc_target=None):
        """CNN_LSTM模型的多任务损失计算"""
        S_target, Pz_target, Yc_target = targets[..., 0], targets[..., 1], targets[..., 2]

        # 主要预测损失
        loss_S = F.smooth_l1_loss(outputs['S'], S_target)
        loss_Pz = F.smooth_l1_loss(outputs['Pz'], Pz_target)
        loss_Yc = F.smooth_l1_loss(outputs['Yc'], Yc_target)

        # 简化的损失权重
        total_loss = 2.0 * loss_S + 1.5 * loss_Pz + 3.0 * loss_Yc

        return total_loss, {
            'loss_S': loss_S.item(),
            'loss_Pz': loss_Pz.item(),
            'loss_Yc': loss_Yc.item(),
            'loss_recon': 0.0,
            'loss_kl': 0.0,
            'loss_physics': 0.0,
            'total_loss': total_loss.item(),
            'physics_weight': 0.0,
            'data_weight': 1.0
        }

class EnhancedTransformerPredictor(nn.Module):
    """增强的Transformer预测器"""
    def __init__(self, input_dim=20, d_model=512, nhead=16, num_layers=8):
        super(EnhancedTransformerPredictor, self).__init__()
        
        # 输入嵌入
        self.input_embedding = nn.Sequential(
            nn.Linear(input_dim, d_model),
            nn.LayerNorm(d_model),
            nn.GELU(),
            nn.Dropout(0.1)
        )
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(d_model)
        self.learned_pos = nn.Parameter(torch.randn(1, 501, d_model) * 0.02)
        
        # Transformer编码器
        encoder_layer = TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 4,
            dropout=0.1,
            activation='gelu',
            batch_first=True,
            norm_first=True
        )
        self.transformer = TransformerEncoder(encoder_layer, num_layers)
        
        # S和Pz输出头
        self.output_head = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.LayerNorm(d_model // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, d_model // 4),
            nn.GELU(),
            nn.Linear(d_model // 4, 2)
        )

        # Yc预测头
        self.yc_head = nn.Sequential(
            nn.Linear(d_model, d_model // 4),
            nn.LayerNorm(d_model // 4),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 4, d_model // 8),
            nn.GELU(),
            nn.Linear(d_model // 8, 1)
        )
        
    def forward(self, x, height_info=None, mass_info=None):
        batch_size, seq_len, _ = x.shape

        # 输入嵌入
        x = self.input_embedding(x)

        # 位置编码
        x = self.pos_encoding(x) + self.learned_pos[:, :seq_len]

        # Transformer处理
        x = self.transformer(x)

        # S和Pz预测
        sp_output = self.output_head(x)

        # Yc预测 - 使用全局平均池化
        global_features = torch.mean(x, dim=1)  # [B, d_model]

        # 如果有高度和质量信息，融入特征
        if height_info is not None and mass_info is not None:
            height_tensor = torch.tensor(height_info / 200.0, device=x.device, dtype=x.dtype).view(-1, 1)
            mass_tensor = torch.tensor(mass_info / 700.0, device=x.device, dtype=x.dtype).view(-1, 1)

            # 扩展物理信息
            physics_info = torch.cat([height_tensor, mass_tensor], dim=-1)  # [B, 2]
            physics_expanded = physics_info.unsqueeze(1).expand(-1, global_features.size(1) // 2, -1).reshape(batch_size, -1)

            # 融合特征
            if physics_expanded.size(1) <= global_features.size(1):
                global_features[:, :physics_expanded.size(1)] += physics_expanded

        yc_raw = self.yc_head(global_features)  # [B, 1]
        yc_pred = 45 + torch.sigmoid(yc_raw) * 20  # 缩放到[45, 65] MPa，更小范围提高精度
        yc_pred = yc_pred.squeeze(-1).unsqueeze(1).expand(-1, seq_len)  # [B, L]

        return {
            'predictions': sp_output,
            'S': sp_output[..., 0:1].squeeze(-1),
            'Pz': sp_output[..., 1:2].squeeze(-1),
            'Yc': yc_pred.squeeze(-1)
        }

    def compute_multitask_loss(self, outputs, targets, epoch_ratio=0.5, input_data=None, yc_target=None):
        """EnhancedTransformer模型的多任务损失计算"""
        S_target, Pz_target, Yc_target = targets[..., 0], targets[..., 1], targets[..., 2]

        # 主要预测损失
        loss_S = F.smooth_l1_loss(outputs['S'], S_target)
        loss_Pz = F.smooth_l1_loss(outputs['Pz'], Pz_target)
        loss_Yc = F.smooth_l1_loss(outputs['Yc'], Yc_target)

        # 简化的损失权重
        total_loss = 2.0 * loss_S + 1.5 * loss_Pz + 3.0 * loss_Yc

        return total_loss, {
            'loss_S': loss_S.item(),
            'loss_Pz': loss_Pz.item(),
            'loss_Yc': loss_Yc.item(),
            'loss_recon': 0.0,
            'loss_kl': 0.0,
            'loss_physics': 0.0,
            'total_loss': total_loss.item(),
            'physics_weight': 0.0,
            'data_weight': 1.0
        }

class FinalBestSimplifiedPredictor(nn.Module):
    """LSTM启发的简化FinalBest预测器 - 基于LSTM成功经验的简化版本"""

    def __init__(self, input_dim=2, hidden_dim=320, num_layers=4, dropout=0.15, seq_len=100):
        super(FinalBestSimplifiedPredictor, self).__init__()

        self.seq_len = seq_len

        # 1. 简单的2D输入特征扩展 (类似LSTM的简洁性)
        self.input_projection = nn.Sequential(
            nn.Linear(input_dim, hidden_dim//2),
            nn.LayerNorm(hidden_dim//2),
            nn.GELU(),
            nn.Dropout(dropout * 0.2),
            nn.Linear(hidden_dim//2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout * 0.3)
        )

        # 2. 核心LSTM层 (借鉴LSTM的成功架构)
        self.lstm = nn.LSTM(
            hidden_dim, hidden_dim,
            num_layers=num_layers,
            bidirectional=True,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0
        )

        # 3. 简化的输出层 (类似LSTM的直接输出)
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, 3)
        )

    def forward(self, x, height_info=None, mass_info=None):
        """
        简化的前向传播 - 基于LSTM的成功模式
        Args:
            x: [batch_size, seq_len, 2] - 质量和高度的2D输入
        Returns:
            [batch_size, seq_len, 3] - S, Pz, Yc的3D输出
        """
        batch_size, seq_len, _ = x.shape

        # 1. 输入特征投影
        x_projected = self.input_projection(x)  # [B, L, hidden_dim]

        # 2. LSTM处理
        lstm_out, _ = self.lstm(x_projected)  # [B, L, hidden_dim*2]

        # 3. 直接输出预测
        predictions = self.output_layer(lstm_out)  # [B, L, 3]

        return predictions

class FinalBestCompatiblePredictor(nn.Module):
    """兼容版最终最佳预测器 - 保持原有接口的同时使用简化架构"""

    def __init__(self, input_dim=2, d_model=320, nhead=16, num_layers=4, dropout=0.15, seq_len=100):
        super(FinalBestCompatiblePredictor, self).__init__()

        self.seq_len = seq_len
        hidden_dim = d_model

        # 使用简化的预测器作为核心
        self.core_predictor = FinalBestSimplifiedPredictor(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            num_layers=num_layers,
            dropout=dropout,
            seq_len=seq_len
        )

    def forward(self, x, height_info=None, mass_info=None):
        """
        兼容的前向传播 - 保持原有接口
        """
        # 使用简化预测器
        predictions = self.core_predictor(x, height_info, mass_info)

        return predictions

class EnhancedFeaturePredictor(nn.Module):
    """增强特征预测器 - 解决S和Pz预测精度低的问题"""

    def __init__(self, input_dim=20, hidden_dim=256, num_layers=3, dropout=0.1, seq_len=501,
                 use_full_features=True):
        super(EnhancedFeaturePredictor, self).__init__()

        self.seq_len = seq_len
        self.hidden_dim = hidden_dim
        self.use_full_features = use_full_features

        # 1. 特征工程模块 - 关键改进
        if use_full_features:
            # 训练阶段：使用全部传感器数据 + 导数特征
            self.feature_extractor = nn.ModuleDict({
                # 基础传感器特征
                'sensor_features': nn.Sequential(
                    nn.Linear(input_dim, hidden_dim//2),
                    nn.LayerNorm(hidden_dim//2),
                    nn.GELU(),
                    nn.Dropout(dropout * 0.3)
                ),

                # 导数特征处理
                'derivative_features': nn.Sequential(
                    nn.Linear(4, hidden_dim//4),  # dS/dt, d2S/dt2, dPz/dt, d2Pz/dt2
                    nn.LayerNorm(hidden_dim//4),
                    nn.GELU(),
                    nn.Dropout(dropout * 0.3)
                ),

                # 物理交互特征
                'physics_features': nn.Sequential(
                    nn.Linear(6, hidden_dim//4),  # mass, height, mass*height, mass/height, sqrt(mass*height), mass^2/height
                    nn.LayerNorm(hidden_dim//4),
                    nn.GELU(),
                    nn.Dropout(dropout * 0.3)
                )
            })
            feature_dim = hidden_dim
        else:
            # 迁移阶段：仅使用质量和高度
            self.feature_extractor = nn.ModuleDict({
                'basic_features': nn.Sequential(
                    nn.Linear(2, hidden_dim//2),
                    nn.LayerNorm(hidden_dim//2),
                    nn.GELU(),
                    nn.Dropout(dropout * 0.3)
                ),

                'enhanced_physics': nn.Sequential(
                    nn.Linear(6, hidden_dim//2),  # 增强的物理特征
                    nn.LayerNorm(hidden_dim//2),
                    nn.GELU(),
                    nn.Dropout(dropout * 0.3)
                )
            })
            feature_dim = hidden_dim

        # 2. 简化的核心LSTM - 基于成功经验
        self.lstm = nn.LSTM(
            feature_dim, hidden_dim//2,
            num_layers=num_layers,
            bidirectional=True,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0
        )

        # 3. 轻量级注意力
        self.attention = nn.MultiheadAttention(
            hidden_dim, num_heads=4, dropout=dropout, batch_first=True
        )

        # 4. 专门的S和Pz预测头 - 关键改进
        self.s_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim//2),
            nn.LayerNorm(hidden_dim//2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim//2, hidden_dim//4),
            nn.GELU(),
            nn.Linear(hidden_dim//4, 1)
        )

        self.pz_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim//2),
            nn.LayerNorm(hidden_dim//2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim//2, hidden_dim//4),
            nn.GELU(),
            nn.Linear(hidden_dim//4, 1)
        )

        self.yc_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim//4),
            nn.LayerNorm(hidden_dim//4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim//4, 1)
        )

        # 5. 优化的损失权重
        self.loss_weights = {
            'S': 5.0,      # 大幅提高S权重
            'Pz': 3.0,     # 提高Pz权重
            'Yc': 2.0,     # 适度Yc权重
        }

    def forward(self, x, height_info=None, mass_info=None, derivatives=None):
        """
        增强特征前向传播
        Args:
            x: [batch_size, seq_len, input_dim] - 输入特征
            derivatives: dict - 包含导数特征的字典
        Returns:
            [batch_size, seq_len, 3] - S, Pz, Yc的3D输出
        """
        batch_size, seq_len, _ = x.shape

        if self.use_full_features:
            # 训练阶段：使用全部特征
            # 1. 基础传感器特征
            sensor_features = self.feature_extractor['sensor_features'](x)  # [B, L, hidden_dim//2]

            # 2. 导数特征处理
            if derivatives is not None:
                # 构建导数特征向量
                derivative_input = torch.stack([
                    derivatives.get('dS_dt', torch.zeros(batch_size, seq_len, device=x.device)),
                    derivatives.get('d2S_dt2', torch.zeros(batch_size, seq_len, device=x.device)),
                    derivatives.get('dPz_dt', torch.zeros(batch_size, seq_len, device=x.device)),
                    derivatives.get('d2Pz_dt2', torch.zeros(batch_size, seq_len, device=x.device))
                ], dim=-1)  # [B, L, 4]
                derivative_features = self.feature_extractor['derivative_features'](derivative_input)
            else:
                derivative_features = torch.zeros(batch_size, seq_len, self.hidden_dim//4, device=x.device)

            # 3. 物理特征（从质量和高度提取）
            mass_norm = x[:, :, 0] if x.shape[-1] > 0 else torch.ones(batch_size, seq_len, device=x.device) * 0.5
            height_norm = x[:, :, 1] if x.shape[-1] > 1 else torch.ones(batch_size, seq_len, device=x.device) * 0.5

            physics_input = torch.stack([
                mass_norm,
                height_norm,
                mass_norm * height_norm,
                mass_norm / (height_norm + 1e-8),
                torch.sqrt(mass_norm * height_norm + 1e-8),
                mass_norm**2 / (height_norm + 1e-8)
            ], dim=-1)  # [B, L, 6]
            physics_features = self.feature_extractor['physics_features'](physics_input)

            # 特征融合
            combined_features = torch.cat([sensor_features, derivative_features, physics_features], dim=-1)

        else:
            # 迁移阶段：仅使用质量和高度
            mass_norm = x[:, :, 0]
            height_norm = x[:, :, 1]

            # 基础特征
            basic_features = self.feature_extractor['basic_features'](x)

            # 增强物理特征
            physics_input = torch.stack([
                mass_norm,
                height_norm,
                mass_norm * height_norm,
                mass_norm / (height_norm + 1e-8),
                torch.sqrt(mass_norm * height_norm + 1e-8),
                mass_norm**2 / (height_norm + 1e-8)
            ], dim=-1)
            enhanced_physics = self.feature_extractor['enhanced_physics'](physics_input)

            combined_features = torch.cat([basic_features, enhanced_physics], dim=-1)

        # 2. LSTM处理
        lstm_out, _ = self.lstm(combined_features)  # [B, L, hidden_dim]

        # 3. 注意力增强
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)  # [B, L, hidden_dim]

        # 4. 残差连接
        enhanced_features = lstm_out + attn_out  # [B, L, hidden_dim]

        # 5. 专门的预测头
        s_pred = self.s_predictor(enhanced_features).squeeze(-1)  # [B, L]
        pz_pred = self.pz_predictor(enhanced_features).squeeze(-1)  # [B, L]
        yc_pred = self.yc_predictor(enhanced_features).squeeze(-1)  # [B, L]

        # 6. 组合输出
        predictions = torch.stack([s_pred, pz_pred, yc_pred], dim=-1)  # [B, L, 3]

        return predictions

    def compute_multitask_loss(self, outputs, targets, epoch_ratio=0.5, use_focal_loss=True):
        """增强的损失计算 - 重点解决S和Pz预测问题"""
        if isinstance(outputs, torch.Tensor):
            predictions = outputs
        else:
            predictions = outputs['predictions'] if 'predictions' in outputs else outputs

        S_target, Pz_target, Yc_target = targets[..., 0], targets[..., 1], targets[..., 2]
        S_pred, Pz_pred, Yc_pred = predictions[..., 0], predictions[..., 1], predictions[..., 2]

        # 1. 基础损失 - 使用多种损失函数组合
        if use_focal_loss:
            # Focal Loss for hard examples
            def focal_mse_loss(pred, target, alpha=2.0, gamma=2.0):
                mse = F.mse_loss(pred, target, reduction='none')
                focal_weight = torch.pow(mse, gamma)
                return torch.mean(alpha * focal_weight * mse)

            loss_S = focal_mse_loss(S_pred, S_target) + F.smooth_l1_loss(S_pred, S_target)
            loss_Pz = focal_mse_loss(Pz_pred, Pz_target) + F.smooth_l1_loss(Pz_pred, Pz_target)
            loss_Yc = F.smooth_l1_loss(Yc_pred, Yc_target)
        else:
            loss_S = F.smooth_l1_loss(S_pred, S_target) + F.mse_loss(S_pred, S_target)
            loss_Pz = F.smooth_l1_loss(Pz_pred, Pz_target) + F.mse_loss(Pz_pred, Pz_target)
            loss_Yc = F.smooth_l1_loss(Yc_pred, Yc_target)

        # 2. 相关性损失 - 确保预测值与真实值相关
        def correlation_loss(pred, target):
            pred_centered = pred - torch.mean(pred, dim=-1, keepdim=True)
            target_centered = target - torch.mean(target, dim=-1, keepdim=True)

            numerator = torch.sum(pred_centered * target_centered, dim=-1)
            pred_norm = torch.sqrt(torch.sum(pred_centered**2, dim=-1) + 1e-8)
            target_norm = torch.sqrt(torch.sum(target_centered**2, dim=-1) + 1e-8)

            correlation = numerator / (pred_norm * target_norm + 1e-8)
            return torch.mean(1.0 - torch.abs(correlation))  # 最大化相关性

        corr_loss_S = correlation_loss(S_pred, S_target)
        corr_loss_Pz = correlation_loss(Pz_pred, Pz_target)

        # 3. 动态权重调整 - 重点优化S和Pz
        progress_factor = min(1.0, epoch_ratio * 2.0)
        s_weight = self.loss_weights['S'] * (1.0 + 2.0 * progress_factor)  # 5.0 -> 15.0
        pz_weight = self.loss_weights['Pz'] * (1.0 + 1.5 * progress_factor)  # 3.0 -> 7.5
        yc_weight = self.loss_weights['Yc']  # 保持2.0

        # 4. 组合损失
        total_loss = (
            s_weight * (loss_S + 0.5 * corr_loss_S) +
            pz_weight * (loss_Pz + 0.5 * corr_loss_Pz) +
            yc_weight * loss_Yc
        )

        return total_loss, {
            'loss_S': loss_S.item(),
            'loss_Pz': loss_Pz.item(),
            'loss_Yc': loss_Yc.item(),
            'corr_loss_S': corr_loss_S.item(),
            'corr_loss_Pz': corr_loss_Pz.item(),
            'total_loss': total_loss.item(),
            's_weight': s_weight,
            'pz_weight': pz_weight,
            'yc_weight': yc_weight
        }

# 双阶段训练策略类
class TwoStageTrainer:
    """双阶段训练器 - 先用全特征训练，再迁移到简化特征"""

    def __init__(self, full_feature_model, simple_feature_model, device='cuda'):
        self.full_model = full_feature_model
        self.simple_model = simple_feature_model
        self.device = device

    def stage1_train(self, train_loader, epochs=50, lr=1e-3):
        """阶段1：使用全部传感器数据训练"""
        optimizer = torch.optim.AdamW(self.full_model.parameters(), lr=lr, weight_decay=1e-4)
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, epochs)

        self.full_model.train()
        for epoch in range(epochs):
            total_loss = 0
            for batch_idx, (data, targets, derivatives) in enumerate(train_loader):
                data, targets = data.to(self.device), targets.to(self.device)

                optimizer.zero_grad()
                outputs = self.full_model(data, derivatives=derivatives)
                loss, loss_dict = self.full_model.compute_multitask_loss(
                    outputs, targets, epoch_ratio=epoch/epochs
                )

                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.full_model.parameters(), 1.0)
                optimizer.step()

                total_loss += loss.item()

            scheduler.step()
            if epoch % 10 == 0:
                print(f"Stage 1 Epoch {epoch}: Loss = {total_loss/len(train_loader):.6f}")

    def stage2_transfer(self, transfer_loader, epochs=30, lr=5e-4):
        """阶段2：迁移到简化特征"""
        # 复制部分权重
        self._transfer_weights()

        optimizer = torch.optim.AdamW(self.simple_model.parameters(), lr=lr, weight_decay=1e-4)
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, epochs)

        self.simple_model.train()
        for epoch in range(epochs):
            total_loss = 0
            for batch_idx, (data, targets) in enumerate(transfer_loader):
                # 只使用质量和高度
                simple_data = data[:, :, :2].to(self.device)
                targets = targets.to(self.device)

                optimizer.zero_grad()
                outputs = self.simple_model(simple_data)
                loss, loss_dict = self.simple_model.compute_multitask_loss(
                    outputs, targets, epoch_ratio=epoch/epochs
                )

                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.simple_model.parameters(), 1.0)
                optimizer.step()

                total_loss += loss.item()

            scheduler.step()
            if epoch % 10 == 0:
                print(f"Stage 2 Epoch {epoch}: Loss = {total_loss/len(transfer_loader):.6f}")

    def _transfer_weights(self):
        """权重迁移"""
        # 这里可以实现更复杂的权重迁移策略
        print("Transferring weights from full model to simple model...")
        # 简单示例：复制LSTM和注意力层权重
        try:
            self.simple_model.lstm.load_state_dict(self.full_model.lstm.state_dict())
            self.simple_model.attention.load_state_dict(self.full_model.attention.state_dict())
            print("Successfully transferred LSTM and attention weights")
        except:
            print("Weight transfer failed, using random initialization")


# ============================
# Enhanced Model Architecture
# ============================

class PhysicsConstraintModule(nn.Module):
    """物理约束模块 - 基于MATLAB模型的物理约束"""

    def __init__(self, hidden_dim: int = 256):
        super(PhysicsConstraintModule, self).__init__()

        # 物理参数 (基于fun_parameter.m)
        self.register_buffer('M_1', torch.tensor(661.0))  # 主质量
        self.register_buffer('M_2', torch.tensor(24.0))   # 次质量
        self.register_buffer('g', torch.tensor(10.0))     # 重力加速度
        self.register_buffer('K', torch.tensor(3e3))      # 弹簧刚度
        self.register_buffer('mu_f0', torch.tensor(0.55)) # 摩擦系数

        # 物理约束网络
        self.physics_net = nn.Sequential(
            nn.Linear(2, hidden_dim//2),  # 输入: mass, height
            nn.LayerNorm(hidden_dim//2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim//2, hidden_dim//4),
            nn.GELU(),
            nn.Linear(hidden_dim//4, 3)  # 输出: Pz, S, Yc
        )

    def forward(self, height: torch.Tensor, mass: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        基于物理约束计算预测值
        Args:
            height: [B] 高度 (mm)
            mass: [B] 质量 (kg)
        Returns:
            Pz_physics, S_physics, Yc_physics: 物理约束预测值
        """
        batch_size = height.shape[0]

        # 物理特征
        physics_input = torch.stack([
            mass / 3000.0,      # 归一化质量
            height / 3000.0     # 归一化高度
        ], dim=-1)  # [B, 2]

        # 物理约束预测
        physics_output = self.physics_net(physics_input)  # [B, 3]

        Pz_physics = physics_output[:, 0]  # [B]
        S_physics = physics_output[:, 1]   # [B]
        Yc_physics = physics_output[:, 2]  # [B]

        return Pz_physics, S_physics, Yc_physics


class DerivativeFeatureExtractor(nn.Module):
    """导数特征提取器 - 计算dS/dt, d2S/dt2等导数特征"""

    def __init__(self, hidden_dim: int = 256):
        super(DerivativeFeatureExtractor, self).__init__()

        self.hidden_dim = hidden_dim

        # 导数特征处理网络
        self.derivative_net = nn.Sequential(
            nn.Linear(6, hidden_dim//2),  # dS/dt, d2S/dt2, dPz/dt, d2Pz/dt2, dYc/dt, d2Yc/dt2
            nn.LayerNorm(hidden_dim//2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim//2, hidden_dim//4),
            nn.GELU(),
            nn.Linear(hidden_dim//4, hidden_dim//4)
        )

    def compute_derivatives(self, x: torch.Tensor) -> torch.Tensor:
        """
        计算时间序列的一阶和二阶导数
        Args:
            x: [B, L, C] 输入时间序列
        Returns:
            derivatives: [B, L, C*2] 一阶和二阶导数
        """
        batch_size, seq_len, channels = x.shape

        # 一阶导数 (中心差分)
        dx_dt = torch.zeros_like(x)
        dx_dt[:, 1:-1] = (x[:, 2:] - x[:, :-2]) / 2.0
        dx_dt[:, 0] = x[:, 1] - x[:, 0]
        dx_dt[:, -1] = x[:, -1] - x[:, -2]

        # 二阶导数
        d2x_dt2 = torch.zeros_like(x)
        d2x_dt2[:, 1:-1] = x[:, 2:] - 2*x[:, 1:-1] + x[:, :-2]
        d2x_dt2[:, 0] = d2x_dt2[:, 1]
        d2x_dt2[:, -1] = d2x_dt2[:, -2]

        return torch.cat([dx_dt, d2x_dt2], dim=-1)  # [B, L, C*2]

    def forward(self, predictions: torch.Tensor) -> torch.Tensor:
        """
        提取导数特征
        Args:
            predictions: [B, L, 3] 预测的S, Pz, Yc
        Returns:
            derivative_features: [B, L, hidden_dim//4]
        """
        # 计算导数
        derivatives = self.compute_derivatives(predictions)  # [B, L, 6]

        # 处理导数特征
        derivative_features = self.derivative_net(derivatives)  # [B, L, hidden_dim//4]

        return derivative_features


class AdaptiveWeightModule(nn.Module):
    """自适应权重模块 - 动态调整损失权重"""

    def __init__(self, num_outputs: int = 3):
        super(AdaptiveWeightModule, self).__init__()

        self.num_outputs = num_outputs

        # 权重预测网络
        self.weight_net = nn.Sequential(
            nn.Linear(num_outputs, 64),
            nn.LayerNorm(64),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(64, 32),
            nn.GELU(),
            nn.Linear(32, num_outputs),
            nn.Softmax(dim=-1)
        )

        # 初始权重
        self.register_buffer('base_weights', torch.ones(num_outputs))

    def forward(self, losses: torch.Tensor, epoch_ratio: float = 0.5) -> torch.Tensor:
        """
        计算自适应权重
        Args:
            losses: [num_outputs] 各项损失
            epoch_ratio: 训练进度比例
        Returns:
            weights: [num_outputs] 自适应权重
        """
        # 基于损失计算权重
        adaptive_weights = self.weight_net(losses.detach())

        # 训练早期更依赖基础权重，后期更依赖自适应权重
        alpha = min(epoch_ratio * 2, 1.0)
        final_weights = (1 - alpha) * self.base_weights + alpha * adaptive_weights

        return final_weights


class MultiScaleFeatureExtractor(nn.Module):
    """多尺度特征提取器 - 现代卷积架构"""

    def __init__(self, input_dim: int, hidden_dim: int = 256):
        super(MultiScaleFeatureExtractor, self).__init__()

        # 多尺度卷积分支
        self.conv_branches = nn.ModuleList([
            # 短期特征 (kernel_size=3)
            nn.Sequential(
                nn.Conv1d(input_dim, hidden_dim//4, kernel_size=3, padding=1),
                nn.BatchNorm1d(hidden_dim//4),
                nn.GELU(),
                nn.Dropout(0.1)
            ),
            # 中期特征 (kernel_size=7)
            nn.Sequential(
                nn.Conv1d(input_dim, hidden_dim//4, kernel_size=7, padding=3),
                nn.BatchNorm1d(hidden_dim//4),
                nn.GELU(),
                nn.Dropout(0.1)
            ),
            # 长期特征 (kernel_size=15)
            nn.Sequential(
                nn.Conv1d(input_dim, hidden_dim//4, kernel_size=15, padding=7),
                nn.BatchNorm1d(hidden_dim//4),
                nn.GELU(),
                nn.Dropout(0.1)
            ),
            # 全局特征 (全局平均池化)
            nn.Sequential(
                nn.AdaptiveAvgPool1d(1),
                nn.Conv1d(input_dim, hidden_dim//4, kernel_size=1),
                nn.GELU()
            )
        ])

        # 特征融合
        self.feature_fusion = nn.Sequential(
            nn.Conv1d(hidden_dim, hidden_dim, kernel_size=1),
            nn.BatchNorm1d(hidden_dim),
            nn.GELU(),
            nn.Dropout(0.1)
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        多尺度特征提取
        Args:
            x: [B, L, C] 输入特征
        Returns:
            features: [B, L, hidden_dim] 多尺度特征
        """
        x = x.transpose(1, 2)  # [B, C, L]

        # 多尺度特征提取
        branch_features = []
        for i, branch in enumerate(self.conv_branches):
            if i == 3:  # 全局特征分支
                global_feat = branch(x)  # [B, hidden_dim//4, 1]
                global_feat = global_feat.expand(-1, -1, x.shape[-1])  # [B, hidden_dim//4, L]
                branch_features.append(global_feat)
            else:
                branch_features.append(branch(x))

        # 特征拼接
        combined_features = torch.cat(branch_features, dim=1)  # [B, hidden_dim, L]

        # 特征融合
        fused_features = self.feature_fusion(combined_features)  # [B, hidden_dim, L]

        return fused_features.transpose(1, 2)  # [B, L, hidden_dim]


class EnhancedFinalBestPredictor(nn.Module):
    """
    增强版FinalBest预测器 - 集成所有改进功能

    特性:
    1. 双模式输入: 训练时使用全传感器数据，迁移时仅用质量+高度
    2. 特征工程: 包含导数特征 (dS/dt, d2S/dt2)
    3. 物理约束: 基于MATLAB模型的物理约束
    4. 自适应权重: 动态调整损失权重
    5. 现代架构: 多尺度特征提取 + 注意力机制
    """

    def __init__(self,
                 input_dim_full: int = 18,  # 全传感器数据维度
                 input_dim_simple: int = 2,  # 简单输入维度 (质量+高度)
                 hidden_dim: int = 320,
                 num_layers: int = 4,
                 dropout: float = 0.15,
                 seq_len: int = 100,
                 use_physics_constraints: bool = True,
                 use_adaptive_weights: bool = True):
        super(EnhancedFinalBestPredictor, self).__init__()

        self.input_dim_full = input_dim_full
        self.input_dim_simple = input_dim_simple
        self.hidden_dim = hidden_dim
        self.seq_len = seq_len
        self.use_physics_constraints = use_physics_constraints
        self.use_adaptive_weights = use_adaptive_weights

        # 1. 输入处理模块
        # 全传感器数据输入处理
        self.full_input_processor = nn.Sequential(
            nn.Linear(input_dim_full, hidden_dim//2),
            nn.LayerNorm(hidden_dim//2),
            nn.GELU(),
            nn.Dropout(dropout * 0.3),
            nn.Linear(hidden_dim//2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout * 0.3)
        )

        # 简单输入处理 (质量+高度)
        self.simple_input_processor = nn.Sequential(
            nn.Linear(input_dim_simple, hidden_dim//2),
            nn.LayerNorm(hidden_dim//2),
            nn.GELU(),
            nn.Dropout(dropout * 0.2),
            nn.Linear(hidden_dim//2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout * 0.3)
        )

        # 2. 多尺度特征提取
        self.multiscale_extractor = MultiScaleFeatureExtractor(hidden_dim, hidden_dim)

        # 3. 核心LSTM层
        self.lstm = nn.LSTM(
            hidden_dim, hidden_dim,
            num_layers=num_layers,
            bidirectional=True,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0
        )

        # 4. 自注意力机制
        self.attention = nn.MultiheadAttention(
            hidden_dim * 2, num_heads=8, dropout=dropout, batch_first=True
        )

        # 5. 物理约束模块 (暂时禁用以测试性能影响)
        # if use_physics_constraints:
        #     self.physics_module = PhysicsConstraintModule(hidden_dim)

        # 6. 导数特征提取器
        self.derivative_extractor = DerivativeFeatureExtractor(hidden_dim)

        # 7. 自适应权重模块
        if use_adaptive_weights:
            self.adaptive_weights = AdaptiveWeightModule(num_outputs=3)

        # 8. 专门的预测头
        self.s_predictor = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim//2),
            nn.GELU(),
            nn.Linear(hidden_dim//2, 1)
        )

        self.pz_predictor = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim//2),
            nn.GELU(),
            nn.Linear(hidden_dim//2, 1)
        )

        self.yc_predictor = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim//2),
            nn.GELU(),
            nn.Linear(hidden_dim//2, 1)
        )

    def forward(self, x: torch.Tensor,
                mode: str = 'simple',
                height_info: Optional[torch.Tensor] = None,
                mass_info: Optional[torch.Tensor] = None,
                epoch_ratio: float = 0.5) -> Union[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        前向传播
        Args:
            x: 输入数据
                - mode='simple': [B, L, 2] (质量, 高度)
                - mode='full': [B, L, 18] (全传感器数据)
            mode: 'simple' 或 'full'
            height_info: [B] 高度信息 (用于物理约束)
            mass_info: [B] 质量信息 (用于物理约束)
            epoch_ratio: 训练进度比例
        Returns:
            predictions: [B, L, 3] 或包含详细信息的字典
        """
        batch_size, seq_len, input_dim = x.shape

        # 1. 输入处理
        if mode == 'simple':
            # 简单模式: 仅质量+高度
            processed_input = self.simple_input_processor(x)  # [B, L, hidden_dim]

            # 提取质量和高度信息
            if height_info is None:
                height_info = x[:, 0, 1] * 3000.0  # 反归一化高度
            if mass_info is None:
                mass_info = x[:, 0, 0] * 3000.0    # 反归一化质量

        else:
            # 全传感器模式
            processed_input = self.full_input_processor(x)  # [B, L, hidden_dim]

            # 从传感器数据中提取质量和高度 (假设在特定位置)
            if height_info is None:
                height_info = torch.ones(batch_size, device=x.device) * 2000.0  # 默认高度
            if mass_info is None:
                mass_info = torch.ones(batch_size, device=x.device) * 1500.0   # 默认质量

        # 2. 多尺度特征提取
        multiscale_features = self.multiscale_extractor(processed_input)  # [B, L, hidden_dim]

        # 3. LSTM处理
        lstm_out, _ = self.lstm(multiscale_features)  # [B, L, hidden_dim*2]

        # 4. 自注意力增强
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)  # [B, L, hidden_dim*2]

        # 5. 残差连接
        enhanced_features = lstm_out + attn_out  # [B, L, hidden_dim*2]

        # 6. 基础预测
        s_pred = self.s_predictor(enhanced_features).squeeze(-1)   # [B, L]
        pz_pred = self.pz_predictor(enhanced_features).squeeze(-1) # [B, L]
        yc_pred = self.yc_predictor(enhanced_features).squeeze(-1) # [B, L]

        # 组合预测
        predictions = torch.stack([s_pred, pz_pred, yc_pred], dim=-1)  # [B, L, 3]

        # 7. 物理约束 (暂时禁用)
        physics_loss = 0.0
        # if self.use_physics_constraints and self.training:
        #     pz_physics, s_physics, yc_physics = self.physics_module(height_info, mass_info)
        #
        #     # 物理约束损失 (在时间维度上广播)
        #     physics_pred = torch.stack([
        #         s_physics.unsqueeze(1).expand(-1, seq_len),
        #         pz_physics.unsqueeze(1).expand(-1, seq_len),
        #         yc_physics.unsqueeze(1).expand(-1, seq_len)
        #     ], dim=-1)  # [B, L, 3]
        #
        #     physics_loss = F.mse_loss(predictions, physics_pred.detach()) * 0.1

        # 8. 导数特征增强 (训练时)
        if self.training:
            derivative_features = self.derivative_extractor(predictions)  # [B, L, hidden_dim//4]
            # 这里可以进一步使用导数特征来改进预测，暂时作为正则化

        # 返回结果
        if self.training:
            return {
                'predictions': predictions,
                'physics_loss': physics_loss,
                's_pred': s_pred,
                'pz_pred': pz_pred,
                'yc_pred': yc_pred
            }
        else:
            return predictions

    def compute_adaptive_loss(self, predictions: torch.Tensor, targets: torch.Tensor,
                            epoch_ratio: float = 0.5) -> Tuple[torch.Tensor, Dict[str, float]]:
        """
        计算自适应加权损失
        Args:
            predictions: [B, L, 3] 预测值
            targets: [B, L, 3] 目标值
            epoch_ratio: 训练进度比例
        Returns:
            total_loss: 总损失
            loss_dict: 各项损失详情
        """
        # 计算各项基础损失
        s_loss = F.smooth_l1_loss(predictions[..., 0], targets[..., 0])
        pz_loss = F.smooth_l1_loss(predictions[..., 1], targets[..., 1])
        yc_loss = F.smooth_l1_loss(predictions[..., 2], targets[..., 2])

        losses = torch.stack([s_loss, pz_loss, yc_loss])

        # 计算自适应权重
        if self.use_adaptive_weights:
            weights = self.adaptive_weights(losses, epoch_ratio)
        else:
            # 默认权重 (重点关注S和Yc)
            weights = torch.tensor([3.0, 1.0, 3.0], device=losses.device)
            weights = weights / weights.sum()

        # 加权总损失
        total_loss = torch.sum(weights * losses)

        loss_dict = {
            's_loss': s_loss.item(),
            'pz_loss': pz_loss.item(),
            'yc_loss': yc_loss.item(),
            's_weight': weights[0].item(),
            'pz_weight': weights[1].item(),
            'yc_weight': weights[2].item(),
            'total_loss': total_loss.item()
        }

        return total_loss, loss_dict