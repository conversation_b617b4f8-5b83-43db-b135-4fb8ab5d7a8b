#!/usr/bin/env python3
"""
Step 1: 代码结构分析和基本功能测试
"""

import sys
import traceback
import torch
import numpy as np

def test_imports():
    """测试导入依赖"""
    print("🔍 Step 1: 测试导入依赖...")
    
    try:
        from modelgai import (
            EnhancedLandingGearPhysicsModule,
            StreamlinedTransferPredictor,
            OptimizedTransferLearningTrainer,
            AdaptiveWeightController,
            ComprehensiveTestingFramework,
            FinalOptimizedTrainingSystem
        )
        print("✅ 主要类导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        traceback.print_exc()
        return False

def test_basic_instantiation():
    """测试基本实例化"""
    print("\n🔍 测试基本实例化...")
    
    try:
        # 测试物理模块
        physics_module = EnhancedLandingGearPhysicsModule(hidden_dim=256)
        print(f"✅ 物理模块创建成功: {type(physics_module)}")
        
        # 测试主模型
        model = StreamlinedTransferPredictor(input_dim=2, hidden_dim=320)
        print(f"✅ 主模型创建成功: {type(model)}")
        
        # 测试训练系统
        training_system = FinalOptimizedTrainingSystem()
        print(f"✅ 训练系统创建成功: {type(training_system)}")
        
        return True
    except Exception as e:
        print(f"❌ 实例化失败: {e}")
        traceback.print_exc()
        return False

def test_model_parameters():
    """测试模型参数"""
    print("\n🔍 测试模型参数...")
    
    try:
        # 创建物理模块并检查参数
        physics_module = EnhancedLandingGearPhysicsModule()
        
        print("📊 物理模块参数:")
        param_count = 0
        for name, param in physics_module.named_parameters():
            print(f"  - {name}: {param.shape}, 值: {param.item() if param.numel() == 1 else 'tensor'}")
            param_count += param.numel()
        
        print(f"  总参数数量: {param_count}")
        
        # 创建主模型并检查参数
        model = StreamlinedTransferPredictor(input_dim=2, hidden_dim=64)  # 使用较小的hidden_dim测试
        
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"\n📊 主模型参数:")
        print(f"  总参数: {total_params:,}")
        print(f"  可训练参数: {trainable_params:,}")
        
        return True
    except Exception as e:
        print(f"❌ 参数测试失败: {e}")
        traceback.print_exc()
        return False

def test_forward_pass():
    """测试前向传播"""
    print("\n🔍 测试前向传播...")
    
    try:
        # 创建测试数据
        batch_size = 2
        seq_len = 10
        input_dim = 2
        
        # 创建模拟数据 (质量, 高度)
        test_data = torch.randn(batch_size, seq_len, input_dim)
        print(f"📊 测试数据形状: {test_data.shape}")
        print(f"📊 测试数据范围: [{test_data.min():.3f}, {test_data.max():.3f}]")
        
        # 测试物理模块
        physics_module = EnhancedLandingGearPhysicsModule()
        
        # 提取质量和高度 (假设已归一化)
        mass = test_data[:, 0, 0] * 3000  # 反归一化到实际值
        height = test_data[:, 0, 1] * 3000
        
        print(f"📊 质量范围: [{mass.min():.1f}, {mass.max():.1f}] kg")
        print(f"📊 高度范围: [{height.min():.1f}, {height.max():.1f}] mm")
        
        # 物理模块前向传播
        Pz_physics, S_physics, Yc_physics = physics_module(height, mass)
        
        print(f"📊 物理预测结果:")
        print(f"  - Pz: [{Pz_physics.min():.3f}, {Pz_physics.max():.3f}]")
        print(f"  - S: [{S_physics.min():.3f}, {S_physics.max():.3f}]")
        print(f"  - Yc: [{Yc_physics.min():.3f}, {Yc_physics.max():.3f}]")
        
        # 测试主模型
        model = StreamlinedTransferPredictor(input_dim=2, hidden_dim=64, seq_len=seq_len)
        model.eval()
        
        with torch.no_grad():
            outputs = model(test_data)
        
        print(f"📊 模型输出:")
        print(f"  - predictions形状: {outputs['predictions'].shape}")
        print(f"  - S预测范围: [{outputs['S'].min():.3f}, {outputs['S'].max():.3f}]")
        print(f"  - Pz预测范围: [{outputs['Pz'].min():.3f}, {outputs['Pz'].max():.3f}]")
        print(f"  - Yc预测范围: [{outputs['Yc'].min():.3f}, {outputs['Yc'].max():.3f}]")
        
        return True
    except Exception as e:
        print(f"❌ 前向传播测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("="*60)
    print("🚀 Step 1: 代码结构分析和基本功能测试")
    print("="*60)
    
    # 导入必要的模块
    sys.path.append('.')
    
    tests = [
        test_imports,
        test_basic_instantiation,
        test_model_parameters,
        test_forward_pass
    ]
    
    results = []
    for test in tests:
        result = test()
        results.append(result)
        if not result:
            print(f"\n❌ 测试失败，停止后续测试")
            break
    
    print("\n" + "="*60)
    print("📋 Step 1 测试结果总结:")
    print("="*60)
    
    test_names = ["导入测试", "实例化测试", "参数测试", "前向传播测试"]
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    overall_success = all(results)
    print(f"\n🎯 Step 1 整体结果: {'✅ 成功' if overall_success else '❌ 失败'}")
    
    if overall_success:
        print("\n💡 Step 1 完成，可以进行 Step 2: 物理模型验证")
    else:
        print("\n⚠️  需要修复 Step 1 的问题后再继续")
    
    return overall_success

if __name__ == "__main__":
    main()
