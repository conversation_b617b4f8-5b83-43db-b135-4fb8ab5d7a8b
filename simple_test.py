#!/usr/bin/env python3
"""
简单测试脚本
"""

print("开始测试...")

try:
    import torch
    print("✅ PyTorch导入成功")
    print(f"PyTorch版本: {torch.__version__}")
except Exception as e:
    print(f"❌ PyTorch导入失败: {e}")

try:
    from modelgai import EnhancedLandingGearPhysicsModule
    print("✅ 物理模块导入成功")
except Exception as e:
    print(f"❌ 物理模块导入失败: {e}")
    import traceback
    traceback.print_exc()

try:
    from modelgai import StreamlinedTransferPredictor
    print("✅ 主模型导入成功")
except Exception as e:
    print(f"❌ 主模型导入失败: {e}")
    import traceback
    traceback.print_exc()

print("测试完成")
