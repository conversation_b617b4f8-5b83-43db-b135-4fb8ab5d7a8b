#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug data loading issues
"""

import sys
sys.path.append('.')
from enhanced_train import load_and_process_enhanced_data, compute_data_ranges
import os

def debug_data_loading():
    print('🔍 调试数据加载...')
    
    # 计算数据范围
    ranges = compute_data_ranges()
    print(f'数据范围: {ranges}')
    
    # 检查数据目录
    train_dir = "data/Data_Train"
    if not os.path.exists(train_dir):
        print(f'❌ 训练目录不存在: {train_dir}')
        return
    
    # 获取文件列表
    import glob
    train_files = glob.glob(os.path.join(train_dir, "*.txt"))
    print(f'找到 {len(train_files)} 个训练文件')
    
    if not train_files:
        print('❌ 没有找到训练文件')
        return
    
    # 测试加载前几个文件
    success_count = 0
    for i, file_path in enumerate(train_files[:5]):
        filename = os.path.basename(file_path)
        print(f'\n测试文件 {i+1}: {filename}')
        
        try:
            result = load_and_process_enhanced_data(train_dir, filename, ranges, mode='simple')
            
            if result:
                print(f'  ✅ 成功加载')
                print(f'     输入形状: {result["input_data"].shape}')
                print(f'     目标形状: {result["target_data"].shape}')
                print(f'     高度: {result["height"]}, 质量: {result["mass"]}')
                success_count += 1
            else:
                print(f'  ❌ 加载返回None')
        except Exception as e:
            print(f'  ❌ 加载异常: {e}')
    
    print(f'\n总结: {success_count}/{min(5, len(train_files))} 文件加载成功')
    
    if success_count > 0:
        print('✅ 数据加载功能正常')
    else:
        print('❌ 数据加载存在问题')

if __name__ == "__main__":
    debug_data_loading()
