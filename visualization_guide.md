# 数据特征提取和可视化指南

## 📊 数据特征提取

### 1. 从文件名提取质量和高度信息

```python
def extract_info_from_filename(filename):
    """从文件名提取质量和高度信息"""
    import re
    
    patterns = [
        r'(\d+)kg.*?(\d+)mm',  # 质量kg_高度mm
        r'(\d+)mm.*?(\d+)kg',  # 高度mm_质量kg
        r'M(\d+).*?H(\d+)',    # M质量_H高度
        r'H(\d+).*?M(\d+)',    # H高度_M质量
    ]
    
    for pattern in patterns:
        match = re.search(pattern, filename)
        if match:
            num1, num2 = match.groups()
            # 根据上下文判断哪个是质量，哪个是高度
            if 'kg' in filename and 'mm' in filename:
                # 根据关键词判断
                pass
            # 返回 height, mass
    
    return 1500.0, 1500.0  # 默认值
```

### 2. 数据文件读取

```python
def load_data_from_file(file_path):
    """从数据文件读取Pz, S, Yc数据"""
    import pandas as pd
    
    # 尝试不同的读取参数
    for encoding in ['utf-8', 'gbk', 'latin-1']:
        for skip_rows in range(13, 16):
            try:
                df = pd.read_csv(file_path, sep='\t', encoding=encoding, 
                               skiprows=skip_rows, header=None)
                
                if df.shape[1] >= 6 and len(df) > 50:
                    # 提取关键变量
                    time = df.iloc[:, 0].values
                    Pz = df.iloc[:, 3].values  # 第4列是Pz
                    S = df.iloc[:, 5].values   # 第6列是S
                    Yc = S + Pz * 0.01         # Yc计算公式
                    
                    return {
                        'time': time,
                        'Pz': Pz,
                        'S': S,
                        'Yc': Yc
                    }
            except:
                continue
    
    return None
```

### 3. 特征工程

```python
def create_features(data, mass, height):
    """创建训练特征"""
    # 基础特征
    mass_norm = mass / 3000.0  # 归一化质量
    height_norm = height / 3000.0  # 归一化高度
    
    # 导数特征
    dS_dt = np.gradient(data['S'])  # S的一阶导数
    d2S_dt2 = np.gradient(dS_dt)    # S的二阶导数
    dPz_dt = np.gradient(data['Pz']) # Pz的一阶导数
    
    # 物理交互特征
    mass_height_interaction = mass_norm * height_norm
    mass_height_ratio = mass_norm / (height_norm + 1e-8)
    
    return {
        'basic_features': [mass_norm, height_norm],  # 2D迁移学习输入
        'full_features': [mass_norm, height_norm, mass_height_interaction, 
                         mass_height_ratio, ...],  # 完整训练输入
        'targets': [data['S'], data['Pz'], data['Yc']]  # 3D输出
    }
```

## 🎨 可视化需求实现

### 需求1: 不同质量高度组合的响应曲线

```python
def plot_response_curves_by_mass_height():
    """绘制不同质量高度组合的Pz, S, Yc响应曲线"""
    
    # 质量高度组合示例
    combinations = [
        (1522, 717),   # 你提到的例子1
        (1522, 947),   # 你提到的例子2
        (1800, 800),   # 其他组合
        # ... 更多组合
    ]
    
    # 为每个组合分配不同颜色
    colors = plt.cm.tab10(np.linspace(0, 1, len(combinations)))
    
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    for i, (mass, height) in enumerate(combinations):
        # 从对应文件读取数据
        data = load_data_for_mass_height(mass, height)
        
        # 绘制Pz, S, Yc曲线
        axes[0].plot(data['time'], data['Pz'], color=colors[i], 
                    label=f"M={mass}kg, H={height}mm")
        axes[1].plot(data['time'], data['S'], color=colors[i])
        axes[2].plot(data['time'], data['Yc'], color=colors[i])
```

### 需求2: 架构合理性分析

```python
def plot_architecture_justification():
    """说明多尺度卷积、双向LSTM、注意力机制的必要性"""
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. 多尺度特征分析
    # 显示不同时间尺度的特征
    
    # 2. 双向依赖分析  
    # 显示前向和后向时间依赖
    
    # 3. 注意力权重可视化
    # 显示重要时间点的注意力分布
    
    # 4. 频域分析
    # FFT显示多频率成分
    
    # 5. 时序相关性
    # 自相关函数显示长程依赖
    
    # 6. 非线性特征映射
    # 显示复杂的非线性关系
```

### 需求3: 高精度预测图 (Data_Test)

```python
def plot_high_accuracy_predictions():
    """Data_Test中EnhancedFinalBest方法的高精度预测"""
    
    # 加载测试数据和预测结果
    test_data = load_test_data()
    predictions = model.predict(test_data)
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    for sample_idx in range(2):
        for var_idx, var in enumerate(['S', 'Pz', 'Yc']):
            ax = axes[sample_idx, var_idx]
            
            true_values = test_data[sample_idx][var]
            pred_values = predictions[sample_idx][var]
            
            ax.plot(time, true_values, 'b-', label='True Values')
            ax.plot(time, pred_values, 'r--', label='EnhancedFinalBest')
            
            # 计算MAPE
            mape = calculate_mape(pred_values, true_values)
            ax.set_title(f'{var} Prediction (MAPE: {mape:.1f}%)')
```

### 需求4: 无迁移学习差预测图 (Data_Transfer)

```python
def plot_poor_predictions_without_transfer():
    """Data_Transfer中无迁移学习的差预测结果"""
    
    # 使用未经迁移学习的模型
    model_no_transfer = load_model_without_transfer()
    transfer_data = load_transfer_data()
    predictions = model_no_transfer.predict(transfer_data)
    
    # 显示较大的预测误差
    # MAPE通常会比较高 (>20%)
```

### 需求5: 有迁移学习好预测图 (Data_Transfer)

```python
def plot_good_predictions_with_transfer():
    """Data_Transfer中有迁移学习的好预测结果"""
    
    # 对比有无迁移学习的结果
    model_with_transfer = load_enhanced_finalbest_model()
    model_without_transfer = load_baseline_model()
    
    # 在同一图中显示三条线：
    # 1. 真实值
    # 2. 有迁移学习预测 (误差小)
    # 3. 无迁移学习预测 (误差大)
```

### 需求6: 预测值vs真实值对比

```python
def plot_prediction_vs_true_comparison():
    """所有方法的预测值vs真实值散点图"""
    
    methods = ['EnhancedFinalBest', 'CNN', 'LSTM', 'Transformer']
    
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    
    for var_idx, var in enumerate(['S', 'Pz', 'Yc']):
        for method_idx, method in enumerate(methods):
            ax = axes[var_idx, method_idx]
            
            # 收集所有预测值和真实值
            true_vals, pred_vals = collect_all_predictions(method, var)
            
            # 散点图
            ax.scatter(true_vals, pred_vals, alpha=0.6)
            
            # 理想线 y=x
            ax.plot([min_val, max_val], [min_val, max_val], 'k--')
            
            # 计算R²和MAPE
            r2 = calculate_r2(true_vals, pred_vals)
            mape = calculate_mape(pred_vals, true_vals)
            
            ax.set_title(f'{method} - {var}\nR²={r2:.3f}, MAPE={mape:.1f}%')
```

### 需求7: 方法对比总结

```python
def plot_method_comparison_summary():
    """与其他方法对比的性能总结"""
    
    # 基于你提供的实际结果
    methods = ['EnhancedFinalBest', 'CNN', 'LSTM', 'Transformer']
    overall_mape = [10.51, 17.74, 8.54, 13.44]  # 实际数据
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 整体性能对比柱状图
    # 2. 各变量性能对比
    # 3. 性能排名
    # 4. 相对改进百分比
```

## 🔧 实际使用步骤

### 1. 数据准备
```bash
# 确保数据目录结构
data/
├── Data_Train/
├── Data_Test/
└── Data_Transfer/
```

### 2. 运行特征提取
```python
python data_feature_analysis.py
```

### 3. 生成可视化图表
```python
python create_paper_figures.py
```

### 4. 预期输出
- figure1_response_curves_by_mass_height.png
- figure2_architecture_justification.png  
- figure3_high_accuracy_predictions.png
- figure4_poor_predictions_without_transfer.png
- figure5_good_predictions_with_transfer.png
- figure6_prediction_vs_true_comparison.png
- figure7_method_comparison_summary.png

## 📋 关键要点

1. **数据特征**: 质量和高度是关键的2D输入特征，用于迁移学习
2. **目标变量**: Pz, S, Yc是3D输出目标
3. **颜色编码**: 每种质量高度组合使用不同颜色
4. **性能对比**: EnhancedFinalBest方法应该显示最佳性能
5. **迁移学习效果**: 清楚展示迁移学习的改进效果

## 🎯 论文图表说明

这些图表将有力支持你的论文论点：
- 说明数据的复杂性和多样性
- 证明深度学习架构选择的合理性  
- 展示EnhancedFinalBest方法的优越性
- 证明迁移学习的必要性和有效性
