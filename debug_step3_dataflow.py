#!/usr/bin/env python3
"""
Step 3: 数据流测试和NaN问题定位
"""

import torch
import numpy as np
from modelgai import StreamlinedTransferPredictor

def test_complete_forward_pass():
    """测试完整的前向传播，定位NaN来源"""
    print("="*60)
    print("🔍 Step 3: 完整数据流测试")
    print("="*60)
    
    # 创建模型
    model = StreamlinedTransferPredictor(
        input_dim=2, 
        hidden_dim=64,
        seq_len=10,
        use_physics=True
    )
    
    # 创建测试数据
    batch_size = 2
    seq_len = 10
    test_data = torch.randn(batch_size, seq_len, 2) * 0.1 + 0.5
    
    print(f"📊 测试数据:")
    print(f"  形状: {test_data.shape}")
    print(f"  范围: [{test_data.min():.3f}, {test_data.max():.3f}]")
    
    model.eval()
    
    print(f"\n🔍 完整前向传播测试:")
    
    try:
        with torch.no_grad():
            # 完整前向传播
            outputs = model(test_data, epoch_ratio=0.5)
            
            print(f"  输出keys: {list(outputs.keys())}")
            
            # 检查每个输出
            def check_output(name, tensor):
                if tensor is None:
                    print(f"  ⚠️  {name}: None")
                    return True
                
                has_nan = torch.isnan(tensor).any()
                has_inf = torch.isinf(tensor).any()
                
                if has_nan:
                    print(f"  ❌ {name}: 包含NaN")
                    print(f"      形状: {tensor.shape}")
                    print(f"      NaN位置: {torch.isnan(tensor).sum().item()} / {tensor.numel()}")
                    return False
                elif has_inf:
                    print(f"  ❌ {name}: 包含无穷大")
                    return False
                else:
                    print(f"  ✅ {name}: 正常 [{tensor.min():.3f}, {tensor.max():.3f}]")
                    return True
            
            all_good = True
            for key, value in outputs.items():
                if isinstance(value, torch.Tensor):
                    all_good &= check_output(key, value)
                else:
                    print(f"  ℹ️  {key}: {value} (非张量)")
            
            return all_good
            
    except Exception as e:
        print(f"  ❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_physics_fusion_step_by_step():
    """逐步测试物理约束融合过程"""
    print("\n" + "="*60)
    print("🔍 物理约束融合逐步测试")
    print("="*60)
    
    model = StreamlinedTransferPredictor(
        input_dim=2, 
        hidden_dim=64,
        seq_len=10,
        use_physics=True
    )
    
    batch_size = 2
    seq_len = 10
    test_data = torch.randn(batch_size, seq_len, 2) * 0.1 + 0.5
    
    model.eval()
    
    try:
        with torch.no_grad():
            # 手动执行前向传播的每一步
            mass_norm = test_data[:, :, 0]
            height_norm = test_data[:, :, 1]
            
            print(f"1. 特征提取:")
            print(f"   质量: [{mass_norm.min():.3f}, {mass_norm.max():.3f}]")
            print(f"   高度: [{height_norm.min():.3f}, {height_norm.max():.3f}]")
            
            # 基础特征
            basic_features = model.feature_engineering['basic_projection'](test_data[:, :, :2])
            physics_input = model.extract_physics_features(mass_norm, height_norm)
            physics_features = model.feature_engineering['physics_interaction'](physics_input)
            combined_features = torch.cat([basic_features, physics_features], dim=-1)
            
            print(f"2. 特征工程完成: {combined_features.shape}")
            
            # LSTM和注意力
            lstm_out, _ = model.lstm(combined_features)
            attn_out, _ = model.attention(lstm_out, lstm_out, lstm_out)
            enhanced_features = lstm_out + attn_out
            
            print(f"3. 序列处理完成: {enhanced_features.shape}")
            
            # 神经网络预测
            S_nn = model.output_heads['S_head'](enhanced_features).squeeze(-1)
            Pz_nn = model.output_heads['Pz_head'](enhanced_features).squeeze(-1)
            Yc_nn = model.output_heads['Yc_head'](enhanced_features).squeeze(-1)
            
            print(f"4. 神经网络预测:")
            print(f"   S_nn: [{S_nn.min():.3f}, {S_nn.max():.3f}]")
            print(f"   Pz_nn: [{Pz_nn.min():.3f}, {Pz_nn.max():.3f}]")
            print(f"   Yc_nn: [{Yc_nn.min():.3f}, {Yc_nn.max():.3f}]")
            
            # 物理模型预测
            mass_actual = mass_norm * 3000.0
            height_actual = height_norm * 3000.0
            mass_avg = mass_actual.mean(dim=1)
            height_avg = height_actual.mean(dim=1)
            
            print(f"5. 物理模型输入:")
            print(f"   质量平均: {mass_avg.tolist()}")
            print(f"   高度平均: {height_avg.tolist()}")
            
            Pz_physics, S_physics, Yc_physics = model.physics_module(height_avg, mass_avg)
            
            print(f"6. 物理模型预测:")
            print(f"   Pz_physics: {Pz_physics.tolist()}")
            print(f"   S_physics: {S_physics.tolist()}")
            print(f"   Yc_physics: {Yc_physics.tolist()}")
            
            # 归一化物理预测
            pz_min, pz_max = -1.630496, 203.920620
            s_min, s_max = -536.617130, 45.976616
            yc_min, yc_max = 0, 100
            
            S_physics_norm = (S_physics - s_min) / (s_max - s_min + 1e-8)
            Pz_physics_norm = (Pz_physics - pz_min) / (pz_max - pz_min + 1e-8)
            Yc_physics_norm = Yc_physics / yc_max
            
            print(f"7. 归一化物理预测:")
            print(f"   S_physics_norm: {S_physics_norm.tolist()}")
            print(f"   Pz_physics_norm: {Pz_physics_norm.tolist()}")
            print(f"   Yc_physics_norm: {Yc_physics_norm.tolist()}")
            
            # 检查归一化结果
            if torch.isnan(S_physics_norm).any():
                print(f"   ❌ S_physics_norm包含NaN")
                print(f"      S_physics: {S_physics}")
                print(f"      s_min: {s_min}, s_max: {s_max}")
                return False
            
            if torch.isnan(Pz_physics_norm).any():
                print(f"   ❌ Pz_physics_norm包含NaN")
                return False
                
            if torch.isnan(Yc_physics_norm).any():
                print(f"   ❌ Yc_physics_norm包含NaN")
                return False
            
            # 扩展到序列长度
            S_physics_expanded = S_physics_norm.unsqueeze(1).expand(-1, seq_len)
            Pz_physics_expanded = Pz_physics_norm.unsqueeze(1).expand(-1, seq_len)
            Yc_physics_expanded = Yc_physics_norm.unsqueeze(1).expand(-1, seq_len)
            
            print(f"8. 扩展物理预测: {S_physics_expanded.shape}")
            
            # 自适应权重融合
            physics_weight = torch.sigmoid(model.adaptive_weights['physics_weight'])
            adaptive_physics_weight = physics_weight * (1.0 - 0.5 * 0.5)  # epoch_ratio=0.5
            
            print(f"9. 自适应权重: {adaptive_physics_weight.item():.4f}")
            
            # 融合预测
            S_pred = adaptive_physics_weight * S_physics_expanded + (1 - adaptive_physics_weight) * S_nn
            Pz_pred = adaptive_physics_weight * Pz_physics_expanded + (1 - adaptive_physics_weight) * Pz_nn
            Yc_pred = adaptive_physics_weight * Yc_physics_expanded + (1 - adaptive_physics_weight) * Yc_nn
            
            print(f"10. 最终融合预测:")
            print(f"    S_pred: [{S_pred.min():.3f}, {S_pred.max():.3f}]")
            print(f"    Pz_pred: [{Pz_pred.min():.3f}, {Pz_pred.max():.3f}]")
            print(f"    Yc_pred: [{Yc_pred.min():.3f}, {Yc_pred.max():.3f}]")
            
            # 检查最终结果
            if torch.isnan(S_pred).any() or torch.isnan(Pz_pred).any() or torch.isnan(Yc_pred).any():
                print(f"    ❌ 最终预测包含NaN")
                return False
            
            print(f"    ✅ 所有步骤都正常")
            return True
            
    except Exception as e:
        print(f"❌ 逐步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 开始Step 3: 数据流测试")
    
    # 1. 完整前向传播测试
    forward_ok = test_complete_forward_pass()
    
    # 2. 如果有问题，进行逐步测试
    if not forward_ok:
        print("\n⚠️ 发现问题，进行逐步调试...")
        step_by_step_ok = test_physics_fusion_step_by_step()
    else:
        step_by_step_ok = True
    
    print("\n" + "="*60)
    print("📋 Step 3 测试结果:")
    print("="*60)
    print(f"完整前向传播: {'✅ 正常' if forward_ok else '❌ 有问题'}")
    print(f"逐步调试: {'✅ 正常' if step_by_step_ok else '❌ 有问题'}")
    
    if forward_ok and step_by_step_ok:
        print("\n🎯 Step 3 完成，可以进行 Step 4: 训练过程调试")
    else:
        print("\n⚠️ 需要修复 Step 3 的问题")
    
    return forward_ok and step_by_step_ok

if __name__ == "__main__":
    main()
