#!/usr/bin/env python3
"""
简单图表测试
"""

import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt

def test_simple_figure():
    """测试简单图表生成"""
    print("🎨 测试简单图表生成...")
    
    # 创建测试数据
    x = np.linspace(0, 1, 100)
    y1 = np.sin(2 * np.pi * x)
    y2 = np.cos(2 * np.pi * x)
    
    # 创建图表
    plt.figure(figsize=(10, 6))
    plt.plot(x, y1, 'r-', linewidth=2, label='Sin')
    plt.plot(x, y2, 'b--', linewidth=2, label='Cos')
    plt.xlabel('Time')
    plt.ylabel('Amplitude')
    plt.title('Test Figure')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 保存图片
    plt.savefig('test_figure.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 测试图表已保存为 test_figure.png")

def create_response_curves():
    """创建响应曲线图"""
    print("🎨 创建响应曲线图...")
    
    # 模拟不同质量高度组合
    mass_height_combinations = [
        (1522, 717),   # 你提到的例子1
        (1522, 947),   # 你提到的例子2
        (1800, 800),   # 其他组合
        (2000, 1000),
        (1200, 600),
        (1600, 900)
    ]
    
    # 创建颜色映射
    colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']
    
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('Different Mass-Height Combinations Response Curves', fontsize=16, fontweight='bold')
    
    variables = ['Pz (kN)', 'S (mm)', 'Yc (mm)']
    
    for var_idx, var_name in enumerate(variables):
        ax = axes[var_idx]
        
        for i, (mass, height) in enumerate(mass_height_combinations):
            # 生成响应数据
            t = np.linspace(0, 1, 200)
            
            if 'Pz' in var_name:
                # Pz响应：冲击载荷特征
                base_amplitude = (mass/1000) * (height/1000) * 80
                y = base_amplitude * np.exp(-3*t) * (1 + 0.3*np.sin(15*t))
                y = np.maximum(y, 0)  # Pz不能为负
            elif 'S' in var_name:
                # S响应：支柱压缩特征
                max_compression = -(mass/2000) * (height/1500) * 40
                y = max_compression * (1 - np.exp(-4*t)) * np.cos(2*t)
            else:  # Yc
                # Yc响应：吊篮位移特征
                max_displacement = (mass/3000) * (height/2000) * 25
                y = max_displacement * (1 - np.exp(-2*t))
                y = np.maximum(y, 0)  # Yc不能为负
            
            ax.plot(t, y, color=colors[i], linewidth=2.5, alpha=0.8, 
                   label=f"M={mass}kg, H={height}mm")
        
        ax.set_xlabel('Normalized Time', fontsize=12)
        ax.set_ylabel(var_name, fontsize=12)
        ax.set_title(f'{var_name} Response Curves', fontsize=13, fontweight='bold')
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=9)
    
    plt.tight_layout()
    plt.savefig('response_curves.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 响应曲线图已保存为 response_curves.png")

def create_method_comparison():
    """创建方法对比图"""
    print("🎨 创建方法对比图...")
    
    # 基于你提供的实际结果
    methods = ['EnhancedFinalBest', 'CNN', 'LSTM', 'Transformer']
    overall_mape = [10.51, 17.74, 8.54, 13.44]  # 你提供的实际数据
    
    fig, axes = plt.subplots(1, 2, figsize=(16, 6))
    fig.suptitle('Method Performance Comparison', fontsize=16, fontweight='bold')
    
    # 1. 整体MAPE对比
    ax1 = axes[0]
    colors = ['green', 'red', 'blue', 'orange']
    bars = ax1.bar(methods, overall_mape, color=colors, alpha=0.7, edgecolor='black', linewidth=1)
    ax1.set_ylabel('Average MAPE (%)', fontsize=12)
    ax1.set_title('Overall Performance Comparison', fontweight='bold')
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 添加数值标签
    for bar, value in zip(bars, overall_mape):
        ax1.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.3,
                f'{value:.1f}%', ha='center', va='bottom', fontweight='bold', fontsize=10)
    
    # 2. 性能排名
    ax2 = axes[1]
    sorted_indices = np.argsort(overall_mape)
    sorted_methods = [methods[i] for i in sorted_indices]
    sorted_values = [overall_mape[i] for i in sorted_indices]
    rank_colors = ['gold', 'silver', '#CD7F32', 'gray']  # 金银铜
    
    bars = ax2.barh(sorted_methods, sorted_values, color=rank_colors, alpha=0.8, edgecolor='black')
    ax2.set_xlabel('Average MAPE (%)', fontsize=12)
    ax2.set_title('Performance Ranking (Lower is Better)', fontweight='bold')
    ax2.grid(True, alpha=0.3, axis='x')
    
    # 添加排名标签
    for i, (bar, value) in enumerate(zip(bars, sorted_values)):
        rank = i + 1
        ax2.text(value + 0.3, i, f'#{rank}: {value:.1f}%', va='center', fontweight='bold', fontsize=10)
    
    plt.tight_layout()
    plt.savefig('method_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 方法对比图已保存为 method_comparison.png")

def main():
    """主函数"""
    print("🚀 开始测试图表生成...")
    print("="*50)
    
    try:
        # 1. 测试简单图表
        test_simple_figure()
        
        # 2. 创建响应曲线
        create_response_curves()
        
        # 3. 创建方法对比
        create_method_comparison()
        
        print("\n🎉 所有测试图表生成完成！")
        print("生成的文件:")
        print("  1. test_figure.png")
        print("  2. response_curves.png")
        print("  3. method_comparison.png")
        
    except Exception as e:
        print(f"❌ 生成图表时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
