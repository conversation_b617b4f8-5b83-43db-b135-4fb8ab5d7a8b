#!/usr/bin/env python3
"""
Step 2: 物理模型验证和NaN问题调试
"""

import torch
import numpy as np
from modelgai import EnhancedLandingGearPhysicsModule, StreamlinedTransferPredictor

def debug_physics_module():
    """调试物理模块的详细计算过程"""
    print("="*60)
    print("🔍 Step 2: 物理模块详细调试")
    print("="*60)
    
    # 创建物理模块
    physics_module = EnhancedLandingGearPhysicsModule(hidden_dim=64)
    
    # 测试数据
    height = torch.tensor([1000.0, 2000.0, 3000.0])  # mm
    mass = torch.tensor([500.0, 1000.0, 1500.0])     # kg
    
    print(f"📊 输入数据:")
    print(f"  高度: {height.tolist()} mm")
    print(f"  质量: {mass.tolist()} kg")
    
    # 逐步调试物理计算
    print(f"\n🔍 物理参数检查:")
    print(f"  Aa (主腔面积): {physics_module.Aa.item():.6f}")
    print(f"  Aal (副腔面积): {physics_module.Aal.item():.6f}")
    print(f"  Pa0 (初始压力): {physics_module.Pa0.item():.0f}")
    print(f"  Va0 (初始体积): {physics_module.Va0.item():.6f}")
    print(f"  K (轮胎刚度): {physics_module.K.item():.0f}")
    
    # 手动计算每一步
    print(f"\n🔍 逐步计算过程:")
    
    # 1. 估算S和dS_dt
    g = physics_module.g
    impact_velocity = torch.sqrt(2 * g * height / 1000.0)
    print(f"  1. 冲击速度: {impact_velocity.tolist()}")
    
    height_factor = torch.clamp(height / 3000.0, 0.1, 1.0)
    mass_factor = torch.clamp(mass / 3000.0, 0.1, 1.0)
    S_max = height_factor * mass_factor * 50.0
    S = torch.clamp(S_max, min=-50, max=40)
    dS_dt = -impact_velocity * 100
    
    print(f"  2. S估算: {S.tolist()}")
    print(f"  3. dS_dt估算: {dS_dt.tolist()}")
    
    # 2. 单位转换
    S_m = S / 1000.0
    dS_dt_ms = dS_dt / 1000.0
    
    print(f"  4. S_m: {S_m.tolist()}")
    print(f"  5. dS_dt_ms: {dS_dt_ms.tolist()}")
    
    # 3. 气液压力计算
    compression_ratio = torch.clamp(physics_module.Aal * torch.abs(S_m) / physics_module.Va0, min=0, max=0.9)
    pressure_ratio = torch.pow(1 - compression_ratio + 1e-6, -physics_module.n)
    Fa = physics_module.Aal * (physics_module.Pa0 * pressure_ratio - physics_module.Patm)
    Fa = torch.clamp(Fa, min=0, max=50000)
    
    print(f"  6. 压缩比: {compression_ratio.tolist()}")
    print(f"  7. 压力比: {pressure_ratio.tolist()}")
    print(f"  8. Fa: {Fa.tolist()}")
    
    # 检查是否有NaN或无穷大
    def check_tensor(name, tensor):
        if torch.isnan(tensor).any():
            print(f"  ❌ {name} 包含NaN: {tensor}")
            return False
        if torch.isinf(tensor).any():
            print(f"  ❌ {name} 包含无穷大: {tensor}")
            return False
        print(f"  ✅ {name} 正常")
        return True
    
    print(f"\n🔍 数值检查:")
    all_good = True
    all_good &= check_tensor("S", S)
    all_good &= check_tensor("dS_dt", dS_dt)
    all_good &= check_tensor("compression_ratio", compression_ratio)
    all_good &= check_tensor("pressure_ratio", pressure_ratio)
    all_good &= check_tensor("Fa", Fa)
    
    # 完整前向传播
    print(f"\n🔍 完整前向传播:")
    try:
        Pz, S_out, Yc = physics_module(height, mass)
        print(f"  输出 - Pz: {Pz.tolist()}")
        print(f"  输出 - S: {S_out.tolist()}")
        print(f"  输出 - Yc: {Yc.tolist()}")
        
        all_good &= check_tensor("Pz", Pz)
        all_good &= check_tensor("S_out", S_out)
        all_good &= check_tensor("Yc", Yc)
        
    except Exception as e:
        print(f"  ❌ 前向传播失败: {e}")
        all_good = False
    
    return all_good

def debug_main_model():
    """调试主模型的NaN问题"""
    print("\n" + "="*60)
    print("🔍 主模型NaN调试")
    print("="*60)
    
    # 创建模型
    model = StreamlinedTransferPredictor(
        input_dim=2, 
        hidden_dim=64,
        seq_len=10,
        use_physics=True
    )
    
    # 创建测试数据
    batch_size = 2
    seq_len = 10
    test_data = torch.randn(batch_size, seq_len, 2) * 0.1 + 0.5  # 小范围的归一化数据
    
    print(f"📊 测试数据:")
    print(f"  形状: {test_data.shape}")
    print(f"  范围: [{test_data.min():.3f}, {test_data.max():.3f}]")
    print(f"  质量列: {test_data[:, 0, 0].tolist()}")
    print(f"  高度列: {test_data[:, 0, 1].tolist()}")
    
    model.eval()
    
    # 逐步调试前向传播
    print(f"\n🔍 逐步前向传播调试:")
    
    try:
        with torch.no_grad():
            # 1. 特征工程
            mass_norm = test_data[:, :, 0]
            height_norm = test_data[:, :, 1]
            
            print(f"  1. 归一化特征提取成功")
            print(f"     质量范围: [{mass_norm.min():.3f}, {mass_norm.max():.3f}]")
            print(f"     高度范围: [{height_norm.min():.3f}, {height_norm.max():.3f}]")
            
            # 2. 基础特征投影
            basic_features = model.feature_engineering['basic_projection'](test_data[:, :, :2])
            
            if torch.isnan(basic_features).any():
                print(f"  ❌ 基础特征包含NaN")
                return False
            print(f"  2. 基础特征投影成功: {basic_features.shape}")
            
            # 3. 物理交互特征
            physics_input = model.extract_physics_features(mass_norm, height_norm)
            physics_features = model.feature_engineering['physics_interaction'](physics_input)
            
            if torch.isnan(physics_features).any():
                print(f"  ❌ 物理特征包含NaN")
                return False
            print(f"  3. 物理特征计算成功: {physics_features.shape}")
            
            # 4. 组合特征
            combined_features = torch.cat([basic_features, physics_features], dim=-1)
            print(f"  4. 特征组合成功: {combined_features.shape}")
            
            # 5. LSTM处理
            lstm_out, _ = model.lstm(combined_features)
            
            if torch.isnan(lstm_out).any():
                print(f"  ❌ LSTM输出包含NaN")
                return False
            print(f"  5. LSTM处理成功: {lstm_out.shape}")
            
            # 6. 注意力机制
            attn_out, _ = model.attention(lstm_out, lstm_out, lstm_out)
            
            if torch.isnan(attn_out).any():
                print(f"  ❌ 注意力输出包含NaN")
                return False
            print(f"  6. 注意力机制成功: {attn_out.shape}")
            
            # 7. 输出预测
            enhanced_features = lstm_out + attn_out
            
            S_nn = model.output_heads['S_head'](enhanced_features).squeeze(-1)
            Pz_nn = model.output_heads['Pz_head'](enhanced_features).squeeze(-1)
            Yc_nn = model.output_heads['Yc_head'](enhanced_features).squeeze(-1)
            
            print(f"  7. 神经网络预测:")
            print(f"     S_nn范围: [{S_nn.min():.3f}, {S_nn.max():.3f}]")
            print(f"     Pz_nn范围: [{Pz_nn.min():.3f}, {Pz_nn.max():.3f}]")
            print(f"     Yc_nn范围: [{Yc_nn.min():.3f}, {Yc_nn.max():.3f}]")
            
            if torch.isnan(S_nn).any() or torch.isnan(Pz_nn).any() or torch.isnan(Yc_nn).any():
                print(f"  ❌ 神经网络预测包含NaN")
                return False
            
            print(f"  ✅ 所有步骤都正常，NaN可能来自物理约束融合")
            
    except Exception as e:
        print(f"  ❌ 调试过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def main():
    print("🚀 开始Step 2: 物理模型验证和NaN调试")
    
    # 1. 调试物理模块
    physics_ok = debug_physics_module()
    
    # 2. 调试主模型
    if physics_ok:
        model_ok = debug_main_model()
    else:
        print("❌ 物理模块有问题，跳过主模型调试")
        model_ok = False
    
    print("\n" + "="*60)
    print("📋 Step 2 调试结果:")
    print("="*60)
    print(f"物理模块: {'✅ 正常' if physics_ok else '❌ 有问题'}")
    print(f"主模型: {'✅ 正常' if model_ok else '❌ 有问题'}")
    
    if physics_ok and model_ok:
        print("\n🎯 Step 2 完成，可以进行 Step 3: 数据流测试")
    else:
        print("\n⚠️ 需要修复 Step 2 的问题")
    
    return physics_ok and model_ok

if __name__ == "__main__":
    main()
