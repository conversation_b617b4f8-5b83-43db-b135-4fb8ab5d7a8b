#!/usr/bin/env python3
"""
Step 4: 训练过程调试
"""

import torch
import torch.nn as nn
import torch.optim as optim
from modelgai import StreamlinedTransferPredictor, AdaptiveWeightController

def create_dummy_data(batch_size=4, seq_len=20):
    """创建模拟训练数据"""
    # 输入数据 (质量, 高度) - 归一化
    inputs = torch.randn(batch_size, seq_len, 2) * 0.2 + 0.5
    inputs = torch.clamp(inputs, 0.1, 0.9)  # 确保在合理范围内
    
    # 目标数据 (S, Pz, Yc) - 归一化
    targets = torch.randn(batch_size, seq_len, 3) * 0.3 + 0.5
    targets = torch.clamp(targets, 0.0, 1.0)
    
    return inputs, targets

def test_loss_computation():
    """测试损失函数计算"""
    print("="*60)
    print("🔍 Step 4: 损失函数测试")
    print("="*60)
    
    # 创建模型
    model = StreamlinedTransferPredictor(
        input_dim=2,
        hidden_dim=64,
        seq_len=20,
        use_physics=True
    )
    
    # 创建测试数据
    inputs, targets = create_dummy_data()
    
    print(f"📊 测试数据:")
    print(f"  输入形状: {inputs.shape}")
    print(f"  目标形状: {targets.shape}")
    print(f"  输入范围: [{inputs.min():.3f}, {inputs.max():.3f}]")
    print(f"  目标范围: [{targets.min():.3f}, {targets.max():.3f}]")
    
    model.train()
    
    try:
        # 前向传播
        outputs = model(inputs, epoch_ratio=0.3)
        
        print(f"\n🔍 模型输出:")
        print(f"  预测形状: {outputs['predictions'].shape}")
        print(f"  预测范围: [{outputs['predictions'].min():.3f}, {outputs['predictions'].max():.3f}]")
        
        # 计算损失
        loss, loss_dict = model.compute_multitask_loss(outputs, targets, epoch_ratio=0.3)
        
        print(f"\n📊 损失计算结果:")
        print(f"  总损失: {loss.item():.6f}")
        
        for key, value in loss_dict.items():
            if isinstance(value, (int, float)):
                print(f"  {key}: {value:.6f}")
            else:
                print(f"  {key}: {value}")
        
        # 检查损失是否合理
        if torch.isnan(loss) or torch.isinf(loss):
            print(f"  ❌ 损失包含NaN或无穷大")
            return False
        
        if loss.item() < 0:
            print(f"  ❌ 损失为负数")
            return False
        
        print(f"  ✅ 损失计算正常")
        return True
        
    except Exception as e:
        print(f"❌ 损失计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gradient_computation():
    """测试梯度计算"""
    print("\n" + "="*60)
    print("🔍 梯度计算测试")
    print("="*60)
    
    model = StreamlinedTransferPredictor(
        input_dim=2,
        hidden_dim=64,
        seq_len=20,
        use_physics=True
    )
    
    optimizer = optim.AdamW(model.parameters(), lr=1e-3)
    inputs, targets = create_dummy_data()
    
    model.train()
    
    try:
        # 前向传播
        optimizer.zero_grad()
        outputs = model(inputs, epoch_ratio=0.3)
        loss, loss_dict = model.compute_multitask_loss(outputs, targets, epoch_ratio=0.3)
        
        print(f"📊 反向传播前:")
        print(f"  损失: {loss.item():.6f}")
        
        # 反向传播
        loss.backward()
        
        # 检查梯度
        total_grad_norm = 0
        param_count = 0
        nan_grad_count = 0
        zero_grad_count = 0
        
        for name, param in model.named_parameters():
            if param.grad is not None:
                grad_norm = param.grad.norm().item()
                total_grad_norm += grad_norm
                param_count += 1
                
                if torch.isnan(param.grad).any():
                    nan_grad_count += 1
                    print(f"  ❌ {name}: 梯度包含NaN")
                
                if grad_norm < 1e-8:
                    zero_grad_count += 1
        
        avg_grad_norm = total_grad_norm / param_count if param_count > 0 else 0
        
        print(f"📊 梯度统计:")
        print(f"  参数总数: {param_count}")
        print(f"  平均梯度范数: {avg_grad_norm:.6f}")
        print(f"  NaN梯度数量: {nan_grad_count}")
        print(f"  零梯度数量: {zero_grad_count}")
        
        # 梯度裁剪测试
        grad_norm_before = torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        print(f"  裁剪前梯度范数: {grad_norm_before:.6f}")
        
        # 优化器步骤
        optimizer.step()
        
        print(f"  ✅ 梯度计算和更新正常")
        
        if nan_grad_count > 0:
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 梯度计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_adaptive_weights():
    """测试自适应权重系统"""
    print("\n" + "="*60)
    print("🔍 自适应权重系统测试")
    print("="*60)
    
    model = StreamlinedTransferPredictor(
        input_dim=2,
        hidden_dim=64,
        seq_len=20,
        use_physics=True
    )
    
    weight_controller = AdaptiveWeightController(target_accuracy=0.05)
    inputs, targets = create_dummy_data()
    
    print(f"📊 初始权重:")
    print(f"  物理权重: {model.adaptive_weights['physics_weight'].item():.4f}")
    
    try:
        # 模拟几个训练步骤
        for epoch in range(5):
            model.train()
            outputs = model(inputs, epoch_ratio=epoch/10.0)
            loss, _ = model.compute_multitask_loss(outputs, targets, epoch_ratio=epoch/10.0)
            
            # 模拟精度计算
            predictions = outputs['predictions']
            mape = torch.mean(torch.abs((predictions - targets) / (targets + 1e-8))) * 100
            
            # 更新自适应权重
            weight_info = weight_controller.update_weights(
                model, loss.item(), mape.item(), epoch/10.0
            )
            
            print(f"  Epoch {epoch+1}:")
            print(f"    损失: {loss.item():.6f}")
            print(f"    MAPE: {mape.item():.2f}%")
            print(f"    物理权重: {weight_info['physics_weight']:.4f}")
            print(f"    模型物理权重: {model.adaptive_weights['physics_weight'].item():.4f}")
        
        print(f"  ✅ 自适应权重系统正常")
        return True
        
    except Exception as e:
        print(f"❌ 自适应权重测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_step():
    """测试完整的训练步骤"""
    print("\n" + "="*60)
    print("🔍 完整训练步骤测试")
    print("="*60)
    
    model = StreamlinedTransferPredictor(
        input_dim=2,
        hidden_dim=64,
        seq_len=20,
        use_physics=True
    )
    
    optimizer = optim.AdamW(model.parameters(), lr=1e-3)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=10)
    
    print(f"📊 训练配置:")
    print(f"  优化器: AdamW")
    print(f"  学习率: {optimizer.param_groups[0]['lr']:.2e}")
    print(f"  调度器: CosineAnnealingLR")
    
    try:
        # 模拟训练循环
        for epoch in range(3):
            model.train()
            
            # 创建批次数据
            inputs, targets = create_dummy_data()
            
            # 训练步骤
            optimizer.zero_grad()
            outputs = model(inputs, epoch_ratio=epoch/10.0)
            loss, loss_dict = model.compute_multitask_loss(outputs, targets, epoch_ratio=epoch/10.0)
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()
            scheduler.step()
            
            # 计算精度
            predictions = outputs['predictions']
            mape = torch.mean(torch.abs((predictions - targets) / (targets + 1e-8))) * 100
            
            print(f"  Epoch {epoch+1}:")
            print(f"    损失: {loss.item():.6f}")
            print(f"    MAPE: {mape.item():.2f}%")
            print(f"    学习率: {optimizer.param_groups[0]['lr']:.2e}")
            print(f"    物理权重: {outputs['physics_weight']:.4f}")
        
        print(f"  ✅ 完整训练步骤正常")
        return True
        
    except Exception as e:
        print(f"❌ 训练步骤失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 开始Step 4: 训练过程调试")
    
    tests = [
        ("损失函数计算", test_loss_computation),
        ("梯度计算", test_gradient_computation),
        ("自适应权重", test_adaptive_weights),
        ("完整训练步骤", test_training_step)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 执行测试: {test_name}")
        result = test_func()
        results.append((test_name, result))
        
        if not result:
            print(f"❌ {test_name} 失败，停止后续测试")
            break
    
    print("\n" + "="*60)
    print("📋 Step 4 测试结果:")
    print("="*60)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    overall_success = all(result for _, result in results)
    
    if overall_success:
        print("\n🎯 Step 4 完成，可以进行 Step 5: 精度影响分析")
    else:
        print("\n⚠️ 需要修复 Step 4 的问题")
    
    return overall_success

if __name__ == "__main__":
    main()
