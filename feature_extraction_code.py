
# 数据提取和特征工程代码
import numpy as np
import pandas as pd
import torch

def extract_features_from_file(file_path):
    """从文件提取特征"""
    # 1. 从文件名提取质量和高度
    filename = os.path.basename(file_path)
    height, mass = extract_info_from_filename(filename)
    
    # 2. 读取数据文件
    df = pd.read_csv(file_path, sep='\t', encoding='utf-8', skiprows=14, header=None)
    
    # 3. 提取关键变量
    time = df.iloc[:, 0].values
    Pz = df.iloc[:, 3].values  # 垂直载荷
    S = df.iloc[:, 5].values   # 支柱行程
    Yc = S + Pz * 0.01         # 吊篮位移 (根据实际公式调整)
    
    # 4. 特征工程
    # 基础特征
    mass_norm = mass / 3000.0  # 归一化质量
    height_norm = height / 3000.0  # 归一化高度
    
    # 导数特征
    dS_dt = np.gradient(S)  # S的一阶导数
    d2S_dt2 = np.gradient(dS_dt)  # S的二阶导数
    dPz_dt = np.gradient(Pz)  # Pz的一阶导数
    d2Pz_dt2 = np.gradient(dPz_dt)  # Pz的二阶导数
    
    # 物理交互特征
    mass_height_interaction = mass_norm * height_norm
    mass_height_ratio = mass_norm / (height_norm + 1e-8)
    sqrt_mass_height = np.sqrt(mass_norm * height_norm + 1e-8)
    
    return {
        'mass': mass,
        'height': height,
        'mass_norm': mass_norm,
        'height_norm': height_norm,
        'time': time,
        'Pz': Pz,
        'S': S,
        'Yc': Yc,
        'dS_dt': dS_dt,
        'd2S_dt2': d2S_dt2,
        'dPz_dt': dPz_dt,
        'd2Pz_dt2': d2Pz_dt2,
        'mass_height_interaction': mass_height_interaction,
        'mass_height_ratio': mass_height_ratio,
        'sqrt_mass_height': sqrt_mass_height
    }

def create_training_features(data_list):
    """创建训练特征"""
    # 对于训练阶段：使用全部传感器数据
    features = []
    targets = []
    
    for data in data_list:
        # 输入特征 (20维)
        feature_vector = np.array([
            data['mass_norm'],
            data['height_norm'],
            data['mass_height_interaction'],
            data['mass_height_ratio'],
            data['sqrt_mass_height'],
            # 可以添加更多传感器特征...
        ])
        
        # 目标变量 (3维)
        target_vector = np.array([
            data['S'],
            data['Pz'], 
            data['Yc']
        ])
        
        features.append(feature_vector)
        targets.append(target_vector)
    
    return np.array(features), np.array(targets)

def create_transfer_features(data_list):
    """创建迁移学习特征 (仅质量和高度)"""
    features = []
    targets = []
    
    for data in data_list:
        # 输入特征 (2维) - 仅质量和高度
        feature_vector = np.array([
            data['mass_norm'],
            data['height_norm']
        ])
        
        # 目标变量 (3维)
        target_vector = np.array([
            data['S'],
            data['Pz'],
            data['Yc']
        ])
        
        features.append(feature_vector)
        targets.append(target_vector)
    
    return np.array(features), np.array(targets)
