#!/usr/bin/env python3
"""
Step 1: 代码结构分析和基本功能测试 - 修复版
"""

import sys
import traceback
import torch
import numpy as np

def main():
    print("="*60)
    print("🚀 Step 1: 代码结构分析和基本功能测试")
    print("="*60)
    
    # 测试1: 基本导入
    print("\n🔍 测试1: 基本导入...")
    try:
        print("导入torch...")
        import torch
        print(f"✅ PyTorch版本: {torch.__version__}")
        
        print("导入modelgai...")
        import modelgai
        print("✅ modelgai模块导入成功")
        
        print("导入具体类...")
        from modelgai import EnhancedLandingGearPhysicsModule
        print("✅ EnhancedLandingGearPhysicsModule导入成功")
        
        from modelgai import StreamlinedTransferPredictor
        print("✅ StreamlinedTransferPredictor导入成功")
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        traceback.print_exc()
        return False
    
    # 测试2: 物理模块实例化
    print("\n🔍 测试2: 物理模块实例化...")
    try:
        physics_module = EnhancedLandingGearPhysicsModule(hidden_dim=64)
        print("✅ 物理模块创建成功")
        
        # 检查关键参数
        print(f"  - Aa (主腔面积): {physics_module.Aa.item():.6f}")
        print(f"  - Pa0 (初始压力): {physics_module.Pa0.item():.0f}")
        print(f"  - K (轮胎刚度): {physics_module.K.item():.0f}")
        
    except Exception as e:
        print(f"❌ 物理模块创建失败: {e}")
        traceback.print_exc()
        return False
    
    # 测试3: 物理模块前向传播
    print("\n🔍 测试3: 物理模块前向传播...")
    try:
        # 创建测试数据
        height = torch.tensor([1000.0, 2000.0])  # mm
        mass = torch.tensor([500.0, 1000.0])     # kg
        
        print(f"  输入 - 高度: {height.tolist()} mm")
        print(f"  输入 - 质量: {mass.tolist()} kg")
        
        # 前向传播
        Pz, S, Yc = physics_module(height, mass)
        
        print(f"  输出 - Pz: {Pz.tolist()}")
        print(f"  输出 - S: {S.tolist()}")
        print(f"  输出 - Yc: {Yc.tolist()}")
        
        # 检查输出合理性
        if torch.isnan(Pz).any() or torch.isnan(S).any() or torch.isnan(Yc).any():
            print("❌ 输出包含NaN值")
            return False
        
        print("✅ 物理模块前向传播成功")
        
    except Exception as e:
        print(f"❌ 物理模块前向传播失败: {e}")
        traceback.print_exc()
        return False
    
    # 测试4: 主模型实例化
    print("\n🔍 测试4: 主模型实例化...")
    try:
        model = StreamlinedTransferPredictor(
            input_dim=2, 
            hidden_dim=64,  # 使用较小的维度测试
            seq_len=10,
            use_physics=True
        )
        print("✅ 主模型创建成功")
        
        # 统计参数
        total_params = sum(p.numel() for p in model.parameters())
        print(f"  总参数数量: {total_params:,}")
        
    except Exception as e:
        print(f"❌ 主模型创建失败: {e}")
        traceback.print_exc()
        return False
    
    # 测试5: 主模型前向传播
    print("\n🔍 测试5: 主模型前向传播...")
    try:
        # 创建测试数据
        batch_size = 2
        seq_len = 10
        input_dim = 2
        
        test_data = torch.randn(batch_size, seq_len, input_dim) * 0.5 + 0.5  # 归一化数据
        print(f"  测试数据形状: {test_data.shape}")
        
        model.eval()
        with torch.no_grad():
            outputs = model(test_data)
        
        print(f"  输出keys: {list(outputs.keys())}")
        print(f"  predictions形状: {outputs['predictions'].shape}")
        
        # 检查输出
        predictions = outputs['predictions']
        if torch.isnan(predictions).any():
            print("❌ 预测输出包含NaN值")
            return False
        
        print(f"  S预测范围: [{outputs['S'].min():.3f}, {outputs['S'].max():.3f}]")
        print(f"  Pz预测范围: [{outputs['Pz'].min():.3f}, {outputs['Pz'].max():.3f}]")
        print(f"  Yc预测范围: [{outputs['Yc'].min():.3f}, {outputs['Yc'].max():.3f}]")
        
        print("✅ 主模型前向传播成功")
        
    except Exception as e:
        print(f"❌ 主模型前向传播失败: {e}")
        traceback.print_exc()
        return False
    
    print("\n" + "="*60)
    print("📋 Step 1 测试结果总结:")
    print("="*60)
    print("✅ 1. 基本导入: 通过")
    print("✅ 2. 物理模块实例化: 通过")
    print("✅ 3. 物理模块前向传播: 通过")
    print("✅ 4. 主模型实例化: 通过")
    print("✅ 5. 主模型前向传播: 通过")
    print("\n🎯 Step 1 整体结果: ✅ 成功")
    print("\n💡 可以进行 Step 2: 物理模型验证")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 Step 1 调试完成！")
        else:
            print("\n⚠️ Step 1 需要进一步修复")
    except Exception as e:
        print(f"\n💥 Step 1 发生未捕获的错误: {e}")
        traceback.print_exc()
