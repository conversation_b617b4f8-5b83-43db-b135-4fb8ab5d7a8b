#!/usr/bin/env python3
"""
优化版落锤试验训练代码 - 精简版
目标：快速训练，达到5%误差精度
"""

import numpy as np
import os
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import pandas as pd
import pickle
import warnings
import random
import glob
import time
import matplotlib.pyplot as plt
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from modelgai import *

warnings.filterwarnings('ignore')

# 设置随机种子
def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)

set_seed(42)

# 全局配置
seq_length = 100
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# 数据目录
train_dir = "data/Data_Train"
test_dir = "data/Data_Test"
transfer_dir = "data/Data_Transfer"

# 数据量控制配置
def get_data_config(test_mode=False):
    """
    控制训练使用的数据量
    Args:
        test_mode (bool): True=测试模式(少量数据), False=完整训练模式(全部数据)
    Returns:
        dict: 数据配置参数
    """
    if test_mode:
        # 测试模式：使用少量数据快速验证
        return {
            'max_train_files': 8,      # 最多8个训练文件
            'max_test_files': 4,       # 最多4个测试文件
            'max_val_files': 2,        # 最多2个验证文件
            'augment_factor_train': 2, # 训练数据增强倍数
            'augment_factor_transfer': 4, # 迁移数据增强倍数
            'epochs_finalbest': 30,    # FinalBest训练轮数
            'epochs_baseline': 20,     # 基线模型训练轮数
            'patience_finalbest': 8,   # FinalBest早停耐心
            'patience_baseline': 6,    # 基线模型早停耐心
            'progress_interval': 5,    # 进度显示间隔
            'mode_name': '🧪 测试模式'
        }
    else:
        # 完整训练模式：优化版配置，专注5%精度目标
        return {
            'max_train_files': None,   # 使用所有训练文件
            'max_test_files': None,    # 使用所有测试文件
            'max_val_files': None,     # 使用所有验证文件
            'augment_factor_train': 6, # 适度减少数据增强，避免过拟合
            'augment_factor_transfer': 8, # 适度减少迁移数据增强
            'epochs_finalbest': 300,   # 大幅增加训练轮数
            'epochs_baseline': 80,     # 基线模型训练轮数
            'patience_finalbest': 50,  # 大幅增加早停耐心
            'patience_baseline': 20,   # 基线模型早停耐心
            'progress_interval': 5,    # 更频繁的进度显示
            'mode_name': '🎯 优化版FinalBest - 5%精度目标专用'
        }

class FinalBestPredictor(nn.Module):
    """优化版最终最佳预测器 - 增强时间感知能力，保持零样本迁移能力"""

    def __init__(self, seq_len=100):
        super(FinalBestPredictor, self).__init__()

        # 优化配置参数 - 借鉴LSTM的成功配置
        hidden_dim = 320  # 保持原来的320
        num_layers = 4    # 保持原来的4层
        dropout = 0.12    # 略微降低dropout，提高学习能力
        self.seq_len = seq_len

        # 1. 增强的5D输入特征工程模块 - 支持时间感知
        self.feature_engineering = nn.ModuleDict({
            # 基础物理特征提取 - 处理质量、高度、时间
            'basic_physics': nn.Sequential(
                nn.Linear(5, hidden_dim//2),  # 从2维扩展到5维输入
                nn.LayerNorm(hidden_dim//2),
                nn.GELU(),
                nn.Dropout(dropout * 0.3)
            ),

            # 时间交互特征 - 新增时间相关特征
            'time_interaction': nn.Sequential(
                nn.Linear(6, hidden_dim//4),  # 时间相关的交互特征
                nn.LayerNorm(hidden_dim//4),
                nn.GELU(),
                nn.Dropout(dropout * 0.3)
            ),

            # 物理约束特征 - 基于物理模型的特征
            'physics_constraint': nn.Sequential(
                nn.Linear(4, hidden_dim//4),  # 物理约束特征
                nn.LayerNorm(hidden_dim//4),
                nn.GELU(),
                nn.Dropout(dropout * 0.3)
            )
        })

        # 2. 简化的特征融合层
        self.feature_fusion_input = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout * 0.2)
        )

        # 2.5. 位置编码 - 增强时序建模能力
        self.pos_encoding = nn.Parameter(torch.randn(1, seq_len, hidden_dim // 4) * 0.02)

        # 3. 简化的多尺度卷积层 - 修复维度问题
        conv_out_dim = hidden_dim // 4  # 使用4整除，确保维度正确
        self.multi_scale_conv = nn.ModuleList([
            # 短期特征 (kernel_size=3)
            nn.Sequential(
                nn.Conv1d(hidden_dim, conv_out_dim, kernel_size=3, padding=1),
                nn.BatchNorm1d(conv_out_dim),
                nn.GELU(),
                nn.Dropout(dropout * 0.5)
            ),
            # 中期特征 (kernel_size=5)
            nn.Sequential(
                nn.Conv1d(hidden_dim, conv_out_dim, kernel_size=5, padding=2),
                nn.BatchNorm1d(conv_out_dim),
                nn.GELU(),
                nn.Dropout(dropout * 0.5)
            ),
            # 长期特征 (kernel_size=7)
            nn.Sequential(
                nn.Conv1d(hidden_dim, conv_out_dim, kernel_size=7, padding=3),
                nn.BatchNorm1d(conv_out_dim),
                nn.GELU(),
                nn.Dropout(dropout * 0.5)
            )
        ])

        # 卷积特征融合层
        self.conv_fusion = nn.Sequential(
            nn.Linear(conv_out_dim * 3, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout * 0.3)
        )

        # 4. 核心LSTM层 - 借鉴LSTM模型的成功架构
        self.lstm = nn.LSTM(
            hidden_dim, hidden_dim,
            num_layers=num_layers,
            bidirectional=True,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0
        )

        # 5. 简化的注意力层 - 减少复杂度
        self.attention = nn.MultiheadAttention(
            hidden_dim * 2, num_heads=8, dropout=dropout * 0.5, batch_first=True
        )

        # 6. 统一的输出层 - 借鉴LSTM的简洁设计
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.GELU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(hidden_dim // 2, 3)  # 直接输出S, Pz, Yc
        )

    def forward(self, x):
        """
        优化的前向传播 - 增强时间感知能力
        Args:
            x: [B, L, 5] 输入张量，最后一维为[mass_norm, height_norm, time_norm, time_interaction, energy_decay]
        Returns:
            predictions: [B, L, 3] 输出张量，最后一维为[S, Pz, Yc]
        """
        batch_size, seq_len, _ = x.shape

        # 1. 增强的5D输入特征工程
        mass_norm = x[:, :, 0]  # [B, L]
        height_norm = x[:, :, 1]  # [B, L]
        time_norm = x[:, :, 2]  # [B, L] - 新增时间特征
        time_interaction = x[:, :, 3]  # [B, L] - 时间交互特征
        energy_decay = x[:, :, 4]  # [B, L] - 能量衰减特征

        # 基础物理特征 - 处理所有5维输入
        basic_features = self.feature_engineering['basic_physics'](x)  # [B, L, hidden_dim//2]

        # 时间交互特征 - 新增
        time_interaction_input = torch.stack([
            time_norm,
            time_norm * mass_norm,
            time_norm * height_norm,
            time_interaction,
            energy_decay,
            torch.sin(time_norm * np.pi)  # 周期性特征
        ], dim=-1)
        time_features = self.feature_engineering['time_interaction'](time_interaction_input)  # [B, L, hidden_dim//4]

        # 物理约束特征 - 新增
        physics_input = torch.stack([
            mass_norm * height_norm,  # 乘积交互
            mass_norm / (height_norm + 1e-8),  # 比值交互
            torch.sqrt(mass_norm * height_norm + 1e-8),  # 几何平均
            energy_decay * time_norm  # 能量-时间交互
        ], dim=-1)
        physics_features = self.feature_engineering['physics_constraint'](physics_input)  # [B, L, hidden_dim//4]

        # 2. 特征融合
        all_features = torch.cat([
            basic_features,
            time_features,
            physics_features
        ], dim=-1)  # [B, L, hidden_dim]

        fused_features = self.feature_fusion_input(all_features)  # [B, L, hidden_dim]

        # 2.5. 添加位置编码增强时序建模
        batch_size, current_seq_len, _ = fused_features.shape
        if current_seq_len <= self.pos_encoding.size(1):
            pos_info = self.pos_encoding[:, :current_seq_len, :].expand(batch_size, -1, -1)
            # 简单的位置信息融合 - 通过加法而不是拼接
            # 将位置编码投影到合适的维度
            if not hasattr(self, 'pos_projection'):
                self.pos_projection = nn.Linear(pos_info.size(-1), fused_features.size(-1)).to(fused_features.device)
            pos_projected = self.pos_projection(pos_info)
            fused_features = fused_features + 0.1 * pos_projected  # 加权融合

        # 3. 多尺度卷积处理
        conv_in = fused_features.transpose(1, 2)  # [B, hidden_dim, L]

        # 应用多尺度卷积并拼接
        multi_scale_features = []
        for conv in self.multi_scale_conv:
            multi_scale_features.append(conv(conv_in))

        # 拼接多尺度特征
        multi_scale_out = torch.cat(multi_scale_features, dim=1)  # [B, conv_out_dim * 3, L]
        multi_scale_out = multi_scale_out.transpose(1, 2)  # [B, L, conv_out_dim * 3]

        # 融合多尺度特征到原始维度
        multi_scale_out = self.conv_fusion(multi_scale_out)  # [B, L, hidden_dim]

        # 4. LSTM处理 - 核心特征提取
        lstm_out, _ = self.lstm(multi_scale_out)  # [B, L, hidden_dim*2]

        # 5. 注意力增强
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)  # [B, L, hidden_dim*2]

        # 6. 残差连接 + 统一输出
        enhanced_features = lstm_out + attn_out  # 残差连接
        predictions = self.output_layer(enhanced_features)  # [B, L, 3]

        return predictions

def extract_info_from_filename(filename):
    """从文件名提取质量和高度信息"""
    try:
        parts = filename.replace('.txt', '').split('-')
        if len(parts) >= 3:
            mass = float(parts[1])
            height = float(parts[2])
            return height, mass
    except Exception as e:
        print(f"警告: 无法解析文件名 {filename}: {e}")
    return 1500.0, 1500.0

def robust_load_data(file_path, start_row=14):
    """鲁棒的数据加载"""
    try:
        for encoding in ['utf-8', 'gbk', 'latin-1']:
            for start_skip in [start_row - 1, 13, 14, 15]:
                try:
                    df = pd.read_csv(file_path, sep='\t', skiprows=start_skip,
                                   header=None, encoding=encoding, on_bad_lines='skip')
                    if df.shape[1] >= 6:
                        return df
                except:
                    continue
        return None
    except Exception as e:
        print(f"  ❌ 读取失败: {e}")
        return None

def create_pure_mass_height_features(mass, height, time_steps):
    """创建纯质量+高度特征（2维）- 零样本迁移专用，增加时间感知能力"""
    # 简化的归一化策略 - 确保零样本迁移能力
    mass_norm = mass / 3000.0  # 简单除以3000
    height_norm = height / 3000.0  # 简单除以3000

    # 确保归一化值在合理范围内
    mass_norm = np.clip(mass_norm, 0.1, 1.0)
    height_norm = np.clip(height_norm, 0.1, 1.0)

    # 增加时间维度和物理特征
    features = np.zeros((time_steps, 5))  # 扩展到5维

    # 时间特征 - 关键改进
    time_norm = np.linspace(0, 1, time_steps)

    # 基于物理的冲击特征
    g = 9.81
    impact_velocity = np.sqrt(2 * g * height / 1000.0) if height > 0 else 1.0
    impact_energy = 0.5 * mass * (impact_velocity ** 2)

    for i in range(time_steps):
        features[i, 0] = mass_norm
        features[i, 1] = height_norm
        features[i, 2] = time_norm[i]  # 时间维度 - 关键添加
        features[i, 3] = time_norm[i] * mass_norm * height_norm  # 时间交互项
        # 添加基于物理的衰减特征
        features[i, 4] = np.exp(-time_norm[i] * 5) * (impact_energy / 1000000.0)  # 冲击能量衰减

    return features

def create_extended_features_for_baselines(mass, height, time_steps):
    """为基线模型创建增强特征 - 兼容性保持，增加物理特征"""
    mass_norm = mass / 3000.0
    height_norm = height / 3000.0

    # 确保归一化值在合理范围内
    mass_norm = np.clip(mass_norm, 0.1, 1.0)
    height_norm = np.clip(height_norm, 0.1, 1.0)

    # 扩展特征维度，增加物理相关特征
    features = np.zeros((time_steps, 6))  # 扩展到6维

    # 时间特征
    time_norm = np.linspace(0, 1, time_steps)

    # 基于物理的特征
    g = 9.81
    impact_velocity = np.sqrt(2 * g * height / 1000.0) if height > 0 else 1.0
    impact_energy = 0.5 * mass * (impact_velocity ** 2)

    for i in range(time_steps):
        features[i, 0] = mass_norm
        features[i, 1] = height_norm
        features[i, 2] = time_norm[i]  # 时间维度
        features[i, 3] = time_norm[i] * mass_norm * height_norm  # 时间交互项
        features[i, 4] = np.exp(-time_norm[i] * 5) * (impact_energy / 1000000.0)  # 冲击能量衰减
        features[i, 5] = np.sin(time_norm[i] * np.pi) * mass_norm  # 振荡特征

    return features

def validate_data_variability(S, Pz, Yc, filename):
    """验证数据的变化性 - 调试用"""
    s_std = np.std(S)
    pz_std = np.std(Pz)
    yc_std = np.std(Yc)

    print(f"📊 数据变化性检查 - {filename}:")
    print(f"  S: 范围[{np.min(S):.2f}, {np.max(S):.2f}], 标准差={s_std:.4f}")
    print(f"  Pz: 范围[{np.min(Pz):.2f}, {np.max(Pz):.2f}], 标准差={pz_std:.4f}")
    print(f"  Yc: 范围[{np.min(Yc):.2f}, {np.max(Yc):.2f}], 标准差={yc_std:.4f}")

    if s_std < 1e-3:
        print(f"  ⚠️ 警告: S值变化很小 (std={s_std:.6f})")
    if pz_std < 1e-3:
        print(f"  ⚠️ 警告: Pz值变化很小 (std={pz_std:.6f})")
    if yc_std < 1e-3:
        print(f"  ⚠️ 警告: Yc值变化很小 (std={yc_std:.6f})")

def load_and_process_data_optimized(directory, filename, ranges, add_noise=False, noise_level=0.02, model_type='FinalBest'):
    """优化的数据加载和处理"""
    file_path = os.path.join(directory, filename)
    height, mass = extract_info_from_filename(filename)

    df = robust_load_data(file_path)
    if df is None or df.shape[1] < 6:
        return None

    indices = np.linspace(0, len(df) - 1, seq_length, dtype=int)
    df = df.iloc[indices]

    S = df.iloc[:, 5].values
    Pz = df.iloc[:, 3].values
    Yc = S + Pz * 0.01

    # 数据变化性验证 - 调试用
    validate_data_variability(S, Pz, Yc, filename)

    if np.any(np.isnan(S)) or np.any(np.isnan(Pz)):
        return None

    if add_noise:
        S += np.random.normal(0, noise_level * np.std(S), S.shape)
        Pz += np.random.normal(0, noise_level * np.std(Pz), Pz.shape)
        Yc += np.random.normal(0, noise_level * np.std(Yc), Yc.shape)

    # 根据模型类型选择输入特征
    if model_type == 'FinalBest':
        input_data = create_pure_mass_height_features(mass, height, seq_length)
    else:
        input_data = create_extended_features_for_baselines(mass, height, seq_length)

    s_min, s_max = ranges[5]
    pz_min, pz_max = ranges[3]

    S_norm = (S - s_min) / (s_max - s_min + 1e-8)
    Pz_norm = (Pz - pz_min) / (pz_max - pz_min + 1e-8)
    Yc_norm = (Yc - s_min) / (s_max - s_min + 1e-8)

    target_data = np.column_stack([S_norm, Pz_norm, Yc_norm])

    return {
        'input_data': input_data.astype(np.float32),
        'target_data': target_data.astype(np.float32),
        'height': height,
        'mass': mass,
        'S_raw': S,
        'Pz_raw': Pz,
        'Yc_raw': Yc,
        'filename': filename,
        'source_dir': directory  # 添加源目录信息
    }

def compute_data_ranges():
    """计算数据范围"""
    print("🔍 计算数据范围...")

    all_S, all_Pz = [], []

    for data_dir in [train_dir, test_dir, transfer_dir]:
        if not os.path.exists(data_dir):
            continue

        files = glob.glob(os.path.join(data_dir, "*.txt"))[:20]

        for file_path in files:
            df = robust_load_data(file_path)
            if df is None or df.shape[1] < 6:
                continue

            S = df.iloc[:, 5].values
            Pz = df.iloc[:, 3].values

            S = S[~np.isnan(S)]
            Pz = Pz[~np.isnan(Pz)]

            if len(S) > 0 and len(Pz) > 0:
                all_S.extend(S)
                all_Pz.extend(Pz)

    ranges = {}
    if all_S:
        ranges[5] = [np.min(all_S), np.max(all_S)]
    else:
        ranges[5] = [-100.0, 50.0]

    if all_Pz:
        ranges[3] = [np.min(all_Pz), np.max(all_Pz)]
    else:
        ranges[3] = [-5.0, 200.0]

    return ranges

def advanced_data_augmentation(data_list, augment_factor=8):
    """增强的数据增强策略 - 多样化的增强技术"""
    augmented_data = []

    for data in data_list:
        # 添加原始数据
        augmented_data.append(data)

        for i in range(augment_factor):
            augmented_item = data.copy()

            # 1. 更保守的物理参数扰动 - 进一步减少扰动强度
            # 质量扰动：非常保守的变化范围
            mass_std = 0.003 + 0.001 * (i / augment_factor)  # 进一步减少标准差
            mass_noise = np.random.normal(1.0, mass_std)
            mass_noise = np.clip(mass_noise, 0.95, 1.05)  # 限制在±5%范围内

            # 高度扰动：非常保守的变化范围
            height_std = 0.005 + 0.002 * (i / augment_factor)  # 进一步减少标准差
            height_noise = np.random.normal(1.0, height_std)
            height_noise = np.clip(height_noise, 0.92, 1.08)  # 限制在±8%范围内

            augmented_item['input_data'] = data['input_data'].copy()
            augmented_item['input_data'][:, 0] *= mass_noise
            augmented_item['input_data'][:, 1] *= height_noise

            # 确保输入数据在合理范围内
            augmented_item['input_data'][:, 0] = np.clip(augmented_item['input_data'][:, 0], 0.05, 1.0)
            augmented_item['input_data'][:, 1] = np.clip(augmented_item['input_data'][:, 1], 0.05, 1.0)

            # 2. 更保守的目标数据噪声增强
            target_data = data['target_data'].copy()

            # 基础噪声（全局）- 进一步减少噪声强度
            base_noise_levels = [0.0005, 0.001, 0.0015, 0.002, 0.0025, 0.003, 0.0035, 0.004]  # 进一步减少噪声水平
            base_noise_level = base_noise_levels[i % len(base_noise_levels)]
            base_noise = np.random.normal(0, base_noise_level, target_data.shape)

            # 局部噪声（时间相关）- 进一步减少强度
            local_noise_factor = 0.8 + 0.2 * np.sin(np.linspace(0, 2*np.pi, target_data.shape[0]))  # 减少变化幅度
            local_noise = np.random.normal(0, base_noise_level * 0.15, target_data.shape)  # 进一步减少局部噪声
            local_noise *= local_noise_factor.reshape(-1, 1)

            # 趋势噪声（长期趋势）- 进一步减少强度
            trend_noise = np.random.normal(0, base_noise_level * 0.05, (1, target_data.shape[1]))  # 进一步减少趋势噪声
            trend_noise = np.repeat(trend_noise, target_data.shape[0], axis=0)

            # 组合噪声
            total_noise = base_noise + local_noise + trend_noise

            # 3. 针对不同目标的特定增强
            # S (位移) - 添加振荡模式
            if i % 3 == 0:
                oscillation = 0.001 * np.sin(np.linspace(0, 2*np.pi, target_data.shape[0]))
                total_noise[:, 0] += oscillation

            # Pz (压力) - 添加脉冲模式
            if i % 3 == 1:
                pulse_pos = np.random.randint(10, target_data.shape[0] - 10)
                pulse_width = np.random.randint(3, 8)
                pulse_strength = np.random.uniform(0.002, 0.008)
                total_noise[pulse_pos:pulse_pos+pulse_width, 1] += pulse_strength

            # Yc (篮子位移) - 添加阶跃模式
            if i % 3 == 2:
                step_pos = np.random.randint(20, target_data.shape[0] - 20)
                step_strength = np.random.uniform(-0.003, 0.003)
                total_noise[step_pos:, 2] += step_strength

            augmented_item['target_data'] = target_data + total_noise
            augmented_item['target_data'] = np.clip(augmented_item['target_data'], 0.0, 1.0)

            # 4. 添加标识以便跟踪增强类型
            augmented_item['augmentation_type'] = f'aug_{i}'
            augmented_item['mass_factor'] = mass_noise
            augmented_item['height_factor'] = height_noise

            augmented_data.append(augmented_item)

    return augmented_data

def smart_adaptive_weights(loss_S, loss_Pz, loss_Yc, epoch, epochs, model_name='FinalBest', target_errors=None, loss_history=None):
    """智能自适应权重系统 - 稳定版，防止NaN"""
    if model_name != 'FinalBest':
        return 1.0, 1.0, 1.0

    # 使用更保守的权重，防止梯度爆炸
    progress = epoch / epochs
    s_weight = 5.0 + 2.0 * progress  # 降低基础权重
    pz_weight = 0.8 + 0.2 * progress
    yc_weight = 5.0 + 2.0 * progress

    # 更温和的动态调整，防止权重过大
    if loss_S and loss_Yc and loss_Pz and not (torch.isnan(torch.tensor(loss_S)) or torch.isnan(torch.tensor(loss_Pz)) or torch.isnan(torch.tensor(loss_Yc))):
        if loss_S > 0.08:
            s_weight *= 1.2  # 降低倍数
        if loss_Yc > 0.08:
            yc_weight *= 1.2
        if loss_Pz > 0.12:
            pz_weight *= 1.1

    # 限制权重最大值，防止梯度爆炸
    s_weight = min(s_weight, 15.0)
    pz_weight = min(pz_weight, 2.0)
    yc_weight = min(yc_weight, 15.0)

    return s_weight, pz_weight, yc_weight

def train_optimized_model(model, train_data, val_data, model_name, config):
    """优化的模型训练 - 智能自适应权重 + 优化参数"""
    model = model.to(device)

    # 稳定的训练参数 - 防止NaN
    if model_name == 'FinalBest':
        base_lr = 0.0005  # 更保守的学习率，防止梯度爆炸
        weight_decay = 1e-4  # 适度正则化
        patience = config['patience_finalbest']
        epochs = config['epochs_finalbest']
        T_0 = 50  # 更长的学习率周期
        T_mult = 2
        warmup_epochs = 20  # 更长的预热阶段
    else:
        base_lr = 0.0008
        weight_decay = 1e-4
        patience = config['patience_baseline']
        epochs = config['epochs_baseline']
        T_0 = 30
        T_mult = 2
        warmup_epochs = 5

    # 使用AdamW优化器，优化参数设置
    optimizer = optim.AdamW(
        model.parameters(),
        lr=base_lr,
        weight_decay=weight_decay,
        betas=(0.9, 0.999),
        eps=1e-8,
        amsgrad=True
    )

    # 学习率调度：预热 + 余弦退火
    def lr_lambda(epoch):
        if epoch < warmup_epochs:
            return epoch / warmup_epochs
        return 1.0

    warmup_scheduler = optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)
    cosine_scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=T_0, T_mult=T_mult, eta_min=1e-7
    )

    best_val_loss = float('inf')
    patience_counter = 0
    train_losses = []
    val_losses = []
    loss_history = []  # 用于智能权重调整

    os.makedirs("models", exist_ok=True)

    print(f"🚀 开始训练 {model_name} - 样本: {len(train_data)}/{len(val_data)}")

    for epoch in range(epochs):
        # 训练阶段
        model.train()
        epoch_train_loss = 0
        epoch_train_samples = 0
        epoch_loss_S_sum = 0
        epoch_loss_Pz_sum = 0
        epoch_loss_Yc_sum = 0

        for batch_data in train_data:
            input_data = torch.tensor(batch_data['input_data'], dtype=torch.float32).unsqueeze(0).to(device)
            target_data = torch.tensor(batch_data['target_data'], dtype=torch.float32).unsqueeze(0).to(device)

            optimizer.zero_grad()

            outputs = model(input_data)

            if isinstance(outputs, dict):
                # 对于返回字典的模型（如CNN, LSTM等）
                if 'predictions' in outputs:
                    predictions = outputs['predictions']
                    if predictions.shape[-1] == 2:  # 只有S和Pz
                        # 从字典中获取Yc
                        S_pred = outputs['S']
                        Pz_pred = outputs['Pz']
                        Yc_pred = outputs['Yc']

                        loss_S = F.smooth_l1_loss(S_pred, target_data[..., 0])
                        loss_Pz = F.smooth_l1_loss(Pz_pred, target_data[..., 1])
                        loss_Yc = F.smooth_l1_loss(Yc_pred, target_data[..., 2])
                    else:  # 完整的3维输出
                        loss_S = F.smooth_l1_loss(predictions[..., 0], target_data[..., 0])
                        loss_Pz = F.smooth_l1_loss(predictions[..., 1], target_data[..., 1])
                        loss_Yc = F.smooth_l1_loss(predictions[..., 2], target_data[..., 2])
                else:
                    # 直接从字典获取各项预测
                    S_pred = outputs['S']
                    Pz_pred = outputs['Pz']
                    Yc_pred = outputs['Yc']

                    loss_S = F.smooth_l1_loss(S_pred, target_data[..., 0])
                    loss_Pz = F.smooth_l1_loss(Pz_pred, target_data[..., 1])
                    loss_Yc = F.smooth_l1_loss(Yc_pred, target_data[..., 2])
            else:
                # 对于直接返回tensor的模型
                predictions = outputs
                loss_S = F.smooth_l1_loss(predictions[..., 0], target_data[..., 0])
                loss_Pz = F.smooth_l1_loss(predictions[..., 1], target_data[..., 1])
                loss_Yc = F.smooth_l1_loss(predictions[..., 2], target_data[..., 2])

            # 智能自适应权重
            s_weight, pz_weight, yc_weight = smart_adaptive_weights(
                loss_S, loss_Pz, loss_Yc, epoch, epochs,
                target_errors=[0.05, 0.05, 0.05],
                loss_history=loss_history,
                model_name=model_name
            )

            total_loss = s_weight * loss_S + pz_weight * loss_Pz + yc_weight * loss_Yc

            if torch.isnan(total_loss) or torch.isinf(total_loss):
                continue

            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()

            epoch_train_loss += total_loss.item()
            epoch_loss_S_sum += loss_S.item()
            epoch_loss_Pz_sum += loss_Pz.item()
            epoch_loss_Yc_sum += loss_Yc.item()
            epoch_train_samples += 1

        avg_train_loss = epoch_train_loss / max(epoch_train_samples, 1)
        avg_loss_S = epoch_loss_S_sum / max(epoch_train_samples, 1)
        avg_loss_Pz = epoch_loss_Pz_sum / max(epoch_train_samples, 1)
        avg_loss_Yc = epoch_loss_Yc_sum / max(epoch_train_samples, 1)

        train_losses.append(avg_train_loss)

        # 记录损失历史用于智能权重调整
        loss_history.append({
            'epoch': epoch,
            'loss_S': avg_loss_S,
            'loss_Pz': avg_loss_Pz,
            'loss_Yc': avg_loss_Yc
        })

        # 验证阶段
        model.eval()
        epoch_val_loss = 0
        epoch_val_samples = 0
        val_loss_S_sum = 0
        val_loss_Pz_sum = 0
        val_loss_Yc_sum = 0

        # 获取当前的权重（如果没有训练样本，使用默认权重）
        if epoch_train_samples > 0:
            current_s_weight, current_pz_weight, current_yc_weight = smart_adaptive_weights(
                avg_loss_S, avg_loss_Pz, avg_loss_Yc, epoch, epochs,
                target_errors=[0.05, 0.05, 0.05],
                loss_history=loss_history,
                model_name=model_name
            )
        else:
            current_s_weight, current_pz_weight, current_yc_weight = 1.0, 1.0, 1.0

        with torch.no_grad():
            for batch_data in val_data:
                input_data = torch.tensor(batch_data['input_data'], dtype=torch.float32).unsqueeze(0).to(device)
                target_data = torch.tensor(batch_data['target_data'], dtype=torch.float32).unsqueeze(0).to(device)

                outputs = model(input_data)

                if isinstance(outputs, dict):
                    # 对于返回字典的模型（如CNN, LSTM等）
                    if 'predictions' in outputs:
                        predictions = outputs['predictions']
                        if predictions.shape[-1] == 2:  # 只有S和Pz
                            # 从字典中获取Yc
                            S_pred = outputs['S']
                            Pz_pred = outputs['Pz']
                            Yc_pred = outputs['Yc']

                            loss_S = F.smooth_l1_loss(S_pred, target_data[..., 0])
                            loss_Pz = F.smooth_l1_loss(Pz_pred, target_data[..., 1])
                            loss_Yc = F.smooth_l1_loss(Yc_pred, target_data[..., 2])
                        else:  # 完整的3维输出
                            loss_S = F.smooth_l1_loss(predictions[..., 0], target_data[..., 0])
                            loss_Pz = F.smooth_l1_loss(predictions[..., 1], target_data[..., 1])
                            loss_Yc = F.smooth_l1_loss(predictions[..., 2], target_data[..., 2])
                    else:
                        # 直接从字典获取各项预测
                        S_pred = outputs['S']
                        Pz_pred = outputs['Pz']
                        Yc_pred = outputs['Yc']

                        loss_S = F.smooth_l1_loss(S_pred, target_data[..., 0])
                        loss_Pz = F.smooth_l1_loss(Pz_pred, target_data[..., 1])
                        loss_Yc = F.smooth_l1_loss(Yc_pred, target_data[..., 2])
                else:
                    # 对于直接返回tensor的模型
                    predictions = outputs
                    loss_S = F.smooth_l1_loss(predictions[..., 0], target_data[..., 0])
                    loss_Pz = F.smooth_l1_loss(predictions[..., 1], target_data[..., 1])
                    loss_Yc = F.smooth_l1_loss(predictions[..., 2], target_data[..., 2])

                # 使用相同的权重计算验证损失
                val_loss = current_s_weight * loss_S + current_pz_weight * loss_Pz + current_yc_weight * loss_Yc

                epoch_val_loss += val_loss.item()
                val_loss_S_sum += loss_S.item()
                val_loss_Pz_sum += loss_Pz.item()
                val_loss_Yc_sum += loss_Yc.item()
                epoch_val_samples += 1

        avg_val_loss = epoch_val_loss / max(epoch_val_samples, 1) if epoch_val_samples > 0 else avg_train_loss
        val_losses.append(avg_val_loss)

        # 学习率调度 - 预热期使用预热调度器，之后使用余弦退火
        if model_name == 'FinalBest':
            if epoch < warmup_epochs:
                warmup_scheduler.step()
            else:
                cosine_scheduler.step()
        else:
            # 基线模型使用简单的学习率调度
            if epoch > 0:
                cosine_scheduler.step()

        # 保存最佳模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            patience_counter = 0
            torch.save(model.state_dict(), f"models/{model_name}_optimized_best.pt")
        else:
            patience_counter += 1

        # 早停
        if patience_counter >= patience:
            print(f"⏹️ 早停 at epoch {epoch}")
            break

        # 简化进度显示
        if epoch % 10 == 0:
            avg_val_loss_S = val_loss_S_sum / max(epoch_val_samples, 1)
            avg_val_loss_Pz = val_loss_Pz_sum / max(epoch_val_samples, 1)
            avg_val_loss_Yc = val_loss_Yc_sum / max(epoch_val_samples, 1)

            print(f"Epoch {epoch:3d}: Loss={avg_train_loss:.6f}, Val_Loss={avg_val_loss:.6f}")
            if model_name == 'FinalBest':
                print(f"         Val: S={avg_val_loss_S:.4f}, Pz={avg_val_loss_Pz:.4f}, Yc={avg_val_loss_Yc:.4f}")

    return train_losses, val_losses

def evaluate_optimized_model(model, test_data, model_name, data_ranges):
    """优化的模型评估"""
    model.eval()
    results = []

    print(f"\n🔍 评估 {model_name}")

    with torch.no_grad():
        for batch_data in test_data:
            input_data = torch.tensor(batch_data['input_data'], dtype=torch.float32).unsqueeze(0).to(device)
            # target_data = torch.tensor(batch_data['target_data'], dtype=torch.float32).unsqueeze(0).to(device)

            start_time = time.time()
            outputs = model(input_data)
            inference_time = time.time() - start_time

            # 反归一化
            s_min, s_max = data_ranges[5]
            pz_min, pz_max = data_ranges[3]

            if isinstance(outputs, dict):
                # 对于返回字典的模型（如CNN, LSTM等）
                if 'predictions' in outputs:
                    predictions = outputs['predictions'].squeeze(0).cpu().numpy()
                    if predictions.shape[-1] == 2:  # 只有S和Pz
                        # 从字典中获取Yc
                        S_pred_tensor = outputs['S'].squeeze(0).cpu().numpy()
                        Pz_pred_tensor = outputs['Pz'].squeeze(0).cpu().numpy()
                        Yc_pred_tensor = outputs['Yc'].squeeze(0).cpu().numpy()

                        S_pred = S_pred_tensor * (s_max - s_min) + s_min
                        Pz_pred = Pz_pred_tensor * (pz_max - pz_min) + pz_min
                        Yc_pred = Yc_pred_tensor * (s_max - s_min) + s_min
                    else:  # 完整的3维输出
                        S_pred = predictions[:, 0] * (s_max - s_min) + s_min
                        Pz_pred = predictions[:, 1] * (pz_max - pz_min) + pz_min
                        Yc_pred = predictions[:, 2] * (s_max - s_min) + s_min
                else:
                    # 直接从字典获取各项预测
                    S_pred_tensor = outputs['S'].squeeze(0).cpu().numpy()
                    Pz_pred_tensor = outputs['Pz'].squeeze(0).cpu().numpy()
                    Yc_pred_tensor = outputs['Yc'].squeeze(0).cpu().numpy()

                    S_pred = S_pred_tensor * (s_max - s_min) + s_min
                    Pz_pred = Pz_pred_tensor * (pz_max - pz_min) + pz_min
                    Yc_pred = Yc_pred_tensor * (s_max - s_min) + s_min
            else:
                # 对于直接返回tensor的模型
                predictions = outputs.squeeze(0).cpu().numpy()
                S_pred = predictions[:, 0] * (s_max - s_min) + s_min
                Pz_pred = predictions[:, 1] * (pz_max - pz_min) + pz_min
                Yc_pred = predictions[:, 2] * (s_max - s_min) + s_min

            S_true = batch_data['S_raw']
            Pz_true = batch_data['Pz_raw']
            Yc_true = batch_data['Yc_raw']

            # 计算指标
            mae_S = mean_absolute_error(S_true, S_pred)
            mae_Pz = mean_absolute_error(Pz_true, Pz_pred)
            mae_Yc = mean_absolute_error(Yc_true, Yc_pred)

            # 百分比误差
            s_range = abs(s_max - s_min)
            pz_range = abs(pz_max - pz_min)

            mae_S_percent = (mae_S / s_range) * 100 if s_range > 0 else 0
            mae_Pz_percent = (mae_Pz / pz_range) * 100 if pz_range > 0 else 0
            mae_Yc_percent = (mae_Yc / s_range) * 100 if s_range > 0 else 0

            results.append({
                'filename': batch_data['filename'],
                'height': batch_data['height'],
                'mass': batch_data['mass'],
                'model': model_name,
                'mae_S': mae_S,
                'mae_Pz': mae_Pz,
                'mae_Yc': mae_Yc,
                'mae_S_percent': mae_S_percent,
                'mae_Pz_percent': mae_Pz_percent,
                'mae_Yc_percent': mae_Yc_percent,
                'rmse_S': np.sqrt(mean_squared_error(S_true, S_pred)),
                'rmse_Pz': np.sqrt(mean_squared_error(Pz_true, Pz_pred)),
                'rmse_Yc': np.sqrt(mean_squared_error(Yc_true, Yc_pred)),
                'r2_S': r2_score(S_true, S_pred),
                'r2_Pz': r2_score(Pz_true, Pz_pred),
                'r2_Yc': r2_score(Yc_true, Yc_pred),
                'inference_time': inference_time,
                'S_pred': S_pred,
                'Pz_pred': Pz_pred,
                'Yc_pred': Yc_pred,
                'S_true': S_true,
                'Pz_true': Pz_true,
                'Yc_true': Yc_true
            })

    return results

def create_ensemble_predictions(models, model_data, data_ranges, all_results):
    """创建集成预测 - 智能加权平均"""
    print("🔗 开始集成预测...")

    # 基于各模型性能计算权重
    model_weights = {}
    total_inverse_error = 0

    for model_name in ['FinalBest', 'CNN', 'LSTM', 'Transformer']:
        if model_name in all_results and all_results[model_name]:
            results = all_results[model_name]
            # 计算综合误差（越小越好）
            avg_error = (
                np.mean([r['mae_S_percent'] for r in results]) +
                np.mean([r['mae_Pz_percent'] for r in results]) +
                np.mean([r['mae_Yc_percent'] for r in results])
            ) / 3.0

            # 使用误差的倒数作为权重基础
            inverse_error = 1.0 / (avg_error + 1e-6)
            model_weights[model_name] = inverse_error
            total_inverse_error += inverse_error

    # 归一化权重
    for model_name in model_weights:
        model_weights[model_name] /= total_inverse_error


    if not model_weights:
        print("❌ 没有有效的模型用于集成")
        return None

    # 获取测试数据（使用FinalBest的测试数据作为基准）
    test_data = model_data['FinalBest']['test']
    ensemble_results = []

    print(f"  处理 {len(test_data)} 个测试样本...")

    for batch_data in test_data:
        # 收集所有模型的预测
        model_predictions = {}

        for model_name, model in models.items():
            if model_name not in model_weights:
                continue

            try:
                # 加载最佳模型
                model.load_state_dict(torch.load(f"models/{model_name}_optimized_best.pt"))
                model.eval()

                # 获取对应模型的输入数据
                if model_name == 'FinalBest':
                    input_data = batch_data['input_data']
                else:
                    # 为基线模型创建3维输入
                    mass = batch_data['mass']
                    height = batch_data['height']
                    input_data = create_extended_features_for_baselines(mass, height, seq_length)

                input_tensor = torch.tensor(input_data, dtype=torch.float32).unsqueeze(0).to(device)

                with torch.no_grad():
                    start_time = time.time()
                    outputs = model(input_tensor)
                    inference_time = time.time() - start_time

                    if isinstance(outputs, dict):
                        predictions = outputs['predictions'].squeeze(0).cpu().numpy()
                        pred_data = {
                            'predictions': predictions,
                            'inference_time': inference_time
                        }
                        # 如果有单独的Yc预测，也保存
                        if 'Yc' in outputs:
                            pred_data['Yc_pred'] = outputs['Yc'].squeeze(0).cpu().numpy()
                    else:
                        predictions = outputs.squeeze(0).cpu().numpy()
                        pred_data = {
                            'predictions': predictions,
                            'inference_time': inference_time
                        }

                    model_predictions[model_name] = pred_data

            except Exception as e:

                continue

        if not model_predictions:
            continue

        # 高级加权平均预测 - 任务专门权重
        # 获取第一个模型的预测形状来初始化
        first_pred = list(model_predictions.values())[0]['predictions']
        seq_len = first_pred.shape[0]

        # 计算任务专门的权重 (基于各模型在不同任务上的表现)
        task_weights = {'S': {}, 'Pz': {}, 'Yc': {}}

        # 从all_results中获取各模型在不同任务上的误差
        for model_name in model_predictions.keys():
            if model_name in all_results and all_results[model_name]:
                results = all_results[model_name]
                s_error = np.mean([r['mae_S_percent'] for r in results])
                pz_error = np.mean([r['mae_Pz_percent'] for r in results])
                yc_error = np.mean([r['mae_Yc_percent'] for r in results])

                # 计算任务专门权重 (误差越小权重越大)
                task_weights['S'][model_name] = 1.0 / (s_error + 1e-6)
                task_weights['Pz'][model_name] = 1.0 / (pz_error + 1e-6)
                task_weights['Yc'][model_name] = 1.0 / (yc_error + 1e-6)

        # 归一化任务权重
        for task in ['S', 'Pz', 'Yc']:
            total_weight = sum(task_weights[task].values())
            if total_weight > 0:
                for model_name in task_weights[task]:
                    task_weights[task][model_name] /= total_weight

        weighted_S = np.zeros(seq_len)
        weighted_Pz = np.zeros(seq_len)
        weighted_Yc = np.zeros(seq_len)
        total_inference_time = 0

        for model_name, pred_data in model_predictions.items():
            predictions = pred_data['predictions']

            # 使用任务专门权重
            s_weight = task_weights['S'].get(model_name, model_weights[model_name])
            pz_weight = task_weights['Pz'].get(model_name, model_weights[model_name])
            yc_weight = task_weights['Yc'].get(model_name, model_weights[model_name])

            # 处理不同维度的预测
            if predictions.shape[1] == 3:  # 完整的3维输出
                weighted_S += s_weight * predictions[:, 0]
                weighted_Pz += pz_weight * predictions[:, 1]
                weighted_Yc += yc_weight * predictions[:, 2]
            elif predictions.shape[1] == 2:  # 只有S和Pz，需要从原始数据获取Yc
                weighted_S += s_weight * predictions[:, 0]
                weighted_Pz += pz_weight * predictions[:, 1]
                # 对于CNN等模型，Yc需要从单独的预测中获取
                if 'Yc_pred' in pred_data:
                    weighted_Yc += yc_weight * pred_data['Yc_pred']
                else:
                    # 如果没有单独的Yc预测，使用默认值或跳过
                    weighted_Yc += yc_weight * np.full(seq_len, 0.5)  # 归一化的中间值

            total_inference_time += pred_data['inference_time']

        # 重新组合为完整的预测
        weighted_predictions = np.column_stack([weighted_S, weighted_Pz, weighted_Yc])

        # 反归一化
        s_min, s_max = data_ranges[5]
        pz_min, pz_max = data_ranges[3]

        S_pred = weighted_predictions[:, 0] * (s_max - s_min) + s_min
        Pz_pred = weighted_predictions[:, 1] * (pz_max - pz_min) + pz_min
        Yc_pred = weighted_predictions[:, 2] * (s_max - s_min) + s_min

        S_true = batch_data['S_raw']
        Pz_true = batch_data['Pz_raw']
        Yc_true = batch_data['Yc_raw']

        # 计算指标
        mae_S = mean_absolute_error(S_true, S_pred)
        mae_Pz = mean_absolute_error(Pz_true, Pz_pred)
        mae_Yc = mean_absolute_error(Yc_true, Yc_pred)

        # 百分比误差
        s_range = abs(s_max - s_min)
        pz_range = abs(pz_max - pz_min)

        mae_S_percent = (mae_S / s_range) * 100 if s_range > 0 else 0
        mae_Pz_percent = (mae_Pz / pz_range) * 100 if pz_range > 0 else 0
        mae_Yc_percent = (mae_Yc / s_range) * 100 if s_range > 0 else 0

        ensemble_results.append({
            'filename': batch_data['filename'],
            'height': batch_data['height'],
            'mass': batch_data['mass'],
            'model': 'Ensemble',
            'mae_S': mae_S,
            'mae_Pz': mae_Pz,
            'mae_Yc': mae_Yc,
            'mae_S_percent': mae_S_percent,
            'mae_Pz_percent': mae_Pz_percent,
            'mae_Yc_percent': mae_Yc_percent,
            'rmse_S': np.sqrt(mean_squared_error(S_true, S_pred)),
            'rmse_Pz': np.sqrt(mean_squared_error(Pz_true, Pz_pred)),
            'rmse_Yc': np.sqrt(mean_squared_error(Yc_true, Yc_pred)),
            'r2_S': r2_score(S_true, S_pred),
            'r2_Pz': r2_score(Pz_true, Pz_pred),
            'r2_Yc': r2_score(Yc_true, Yc_pred),
            'inference_time': total_inference_time,
            'S_pred': S_pred,
            'Pz_pred': Pz_pred,
            'Yc_pred': Yc_pred,
            'S_true': S_true,
            'Pz_true': Pz_true,
            'Yc_true': Yc_true,
            'model_weights': model_weights.copy()
        })

    print(f"✅ 集成预测完成，处理了 {len(ensemble_results)} 个样本")
    return ensemble_results

def create_optimized_visualizations(all_results, model_names):
    """创建优化的可视化图表"""
    os.makedirs("visualizations", exist_ok=True)

    # 1. 性能对比图
    plt.figure(figsize=(15, 10))

    metrics = ['mae_S_percent', 'mae_Pz_percent', 'mae_Yc_percent']
    metric_names = ['S Error (%)', 'Pz Error (%)', 'Yc Error (%)']

    for i, (metric, name) in enumerate(zip(metrics, metric_names)):
        plt.subplot(2, 2, i+1)

        model_errors = []
        model_labels = []

        for model_name in model_names:
            if model_name in all_results and all_results[model_name]:
                errors = [r[metric] for r in all_results[model_name]]
                model_errors.append(errors)
                model_labels.append(model_name)

        if model_errors:
            plt.boxplot(model_errors, labels=model_labels)
            plt.title(f'{name} Distribution')
            plt.ylabel('Error (%)')
            plt.xticks(rotation=45)
            plt.grid(True, alpha=0.3)

            # 添加5%目标线
            plt.axhline(y=5.0, color='red', linestyle='--', alpha=0.7, label='5% Target')
            plt.legend()

    # 4. 综合性能雷达图
    plt.subplot(2, 2, 4)

    summary_data = []
    for model_name in model_names:
        if model_name in all_results and all_results[model_name]:
            results = all_results[model_name]
            avg_s = np.mean([r['mae_S_percent'] for r in results])
            avg_pz = np.mean([r['mae_Pz_percent'] for r in results])
            avg_yc = np.mean([r['mae_Yc_percent'] for r in results])
            summary_data.append([model_name, avg_s, avg_pz, avg_yc])

    if summary_data:
        df_summary = pd.DataFrame(summary_data, columns=['Model', 'S_Error', 'Pz_Error', 'Yc_Error'])

        x = np.arange(len(df_summary))
        width = 0.25

        plt.bar(x - width, df_summary['S_Error'], width, label='S Error (%)', alpha=0.8)
        plt.bar(x, df_summary['Pz_Error'], width, label='Pz Error (%)', alpha=0.8)
        plt.bar(x + width, df_summary['Yc_Error'], width, label='Yc Error (%)', alpha=0.8)

        plt.xlabel('Models')
        plt.ylabel('Error (%)')
        plt.title('Average Performance Comparison')
        plt.xticks(x, df_summary['Model'], rotation=45)
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 添加5%目标线
        plt.axhline(y=5.0, color='red', linestyle='--', alpha=0.7, label='5% Target')

    plt.tight_layout()
    plt.savefig('visualizations/optimized_performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("✅ 可视化图表已生成！")

# 基线模型定义（简化版本）
class SimpleCNNPredictor(nn.Module):
    """简化的CNN预测器 - 支持增强特征"""
    def __init__(self, input_dim=6):  # 更新输入维度
        super(SimpleCNNPredictor, self).__init__()

        self.conv_layers = nn.Sequential(
            nn.Conv1d(input_dim, 64, kernel_size=5, padding=2),
            nn.BatchNorm1d(64),
            nn.GELU(),
            nn.Conv1d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm1d(128),
            nn.GELU(),
            nn.Conv1d(128, 256, kernel_size=3, padding=1),
            nn.BatchNorm1d(256),
            nn.GELU(),
        )

        self.output_layer = nn.Conv1d(256, 3, kernel_size=1)

    def forward(self, x, _height_info=None, _mass_info=None):
        x = x.transpose(1, 2)  # [B, C, L]
        x = self.conv_layers(x)
        predictions = self.output_layer(x)
        predictions = predictions.transpose(1, 2)  # [B, L, C]

        return {
            'predictions': predictions,
            'S': predictions[..., 0],
            'Pz': predictions[..., 1],
            'Yc': predictions[..., 2]
        }

class SimpleLSTMPredictor(nn.Module):
    """简化的LSTM预测器 - 支持增强特征"""
    def __init__(self, input_dim=6, hidden_dim=256):  # 更新输入维度
        super(SimpleLSTMPredictor, self).__init__()

        self.lstm = nn.LSTM(
            input_dim, hidden_dim,
            num_layers=3,
            bidirectional=True,
            batch_first=True,
            dropout=0.1
        )

        self.output_layer = nn.Linear(hidden_dim * 2, 3)

    def forward(self, x, _height_info=None, _mass_info=None):
        lstm_out, _ = self.lstm(x)
        predictions = self.output_layer(lstm_out)

        return {
            'predictions': predictions,
            'S': predictions[..., 0],
            'Pz': predictions[..., 1],
            'Yc': predictions[..., 2]
        }

class SimpleTransformerPredictor(nn.Module):
    """简化的Transformer预测器 - 支持增强特征"""
    def __init__(self, input_dim=6, d_model=256, nhead=8, num_layers=4):  # 更新输入维度
        super(SimpleTransformerPredictor, self).__init__()

        self.input_projection = nn.Linear(input_dim, d_model)

        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 2,
            dropout=0.1,
            activation='gelu',
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers)

        self.output_layer = nn.Linear(d_model, 3)

    def forward(self, x, _height_info=None, _mass_info=None):
        x = self.input_projection(x)
        x = self.transformer(x)
        predictions = self.output_layer(x)

        return {
            'predictions': predictions,
            'S': predictions[..., 0],
            'Pz': predictions[..., 1],
            'Yc': predictions[..., 2]
        }

def debug_step_by_step(test_mode=True):
    """
    分步调试策略 - 从简单到复杂逐步优化
    Args:
        test_mode (bool): True=测试模式(少量数据), False=完整训练模式(全部数据)
    """
    print("\n" + "="*80)
    print("🔧 分步调试策略 - 2D输入到3D输出零样本迁移")
    print("   Step 1: 验证数据加载和预处理")
    print("   Step 2: 测试简单模型架构")
    print("   Step 3: 验证训练流程")
    print("   Step 4: 优化到5%精度目标")
    print("="*80)

    config = get_data_config(test_mode)

    # Step 1: 验证数据加载
    print("\n🔍 Step 1: 验证数据加载和预处理")
    data_ranges = compute_data_ranges()
    print(f"✓ 数据范围计算完成: S={data_ranges[5]}, Pz={data_ranges[3]}")

    # 加载少量数据进行验证
    test_files = [f for f in os.listdir(test_dir) if f.endswith('.txt')][:2]
    print(f"✓ 测试文件: {test_files}")

    for filename in test_files:
        # 测试FinalBest模型的2D输入
        result_2d = load_and_process_data_optimized(test_dir, filename, data_ranges, add_noise=False, model_type='FinalBest')
        if result_2d:
            print(f"  ✓ {filename}: 2D输入形状 {result_2d['input_data'].shape}, 输出形状 {result_2d['target_data'].shape}")
            print(f"    质量: {result_2d['mass']:.1f}kg, 高度: {result_2d['height']:.1f}mm")

        # 测试基线模型的3D输入
        result_3d = load_and_process_data_optimized(test_dir, filename, data_ranges, add_noise=False, model_type='CNN')
        if result_3d:
            print(f"  ✓ {filename}: 3D输入形状 {result_3d['input_data'].shape}, 输出形状 {result_3d['target_data'].shape}")

    # Step 2: 测试简单模型架构
    print("\n🏗️ Step 2: 测试模型架构")

    # 创建简化的FinalBest模型进行测试
    test_model = FinalBestPredictor(seq_len=seq_length)
    test_model = test_model.to(device)

    # 测试前向传播
    if result_2d:
        test_input = torch.tensor(result_2d['input_data'], dtype=torch.float32).unsqueeze(0).to(device)
        print(f"  测试输入形状: {test_input.shape}")

        try:
            with torch.no_grad():
                test_output = test_model(test_input)
            print(f"  ✓ 模型前向传播成功，输出形状: {test_output.shape}")
            print(f"  ✓ 输出范围: S=[{test_output[0,:,0].min():.3f}, {test_output[0,:,0].max():.3f}]")
            print(f"              Pz=[{test_output[0,:,1].min():.3f}, {test_output[0,:,1].max():.3f}]")
            print(f"              Yc=[{test_output[0,:,2].min():.3f}, {test_output[0,:,2].max():.3f}]")
        except Exception as e:
            print(f"  ❌ 模型前向传播失败: {e}")
            return False

    # Step 3: 验证训练流程
    print("\n🎓 Step 3: 验证训练流程（快速测试）")

    # 准备少量训练数据
    train_files = [f for f in os.listdir(train_dir) if f.endswith('.txt')][:2]
    train_data = []
    for filename in train_files:
        result = load_and_process_data_optimized(train_dir, filename, data_ranges, add_noise=True, model_type='FinalBest')
        if result:
            train_data.append(result)

    val_data = [result_2d] if result_2d else []

    if train_data and val_data:
        print(f"  训练数据: {len(train_data)} 样本")
        print(f"  验证数据: {len(val_data)} 样本")

        # 快速训练测试（只训练几个epoch）
        test_config = config.copy()
        test_config['epochs_finalbest'] = 5
        test_config['patience_finalbest'] = 3

        try:
            train_losses, val_losses = train_optimized_model(
                test_model, train_data, val_data, 'FinalBest', test_config
            )
            print(f"  ✓ 训练流程验证成功，最终训练损失: {train_losses[-1]:.6f}")
            print(f"  ✓ 最终验证损失: {val_losses[-1]:.6f}")
        except Exception as e:
            print(f"  ❌ 训练流程验证失败: {e}")
            return False

    print("\n✅ 分步调试完成，所有步骤验证成功！")
    print("🚀 可以进行完整训练...")
    return True

def main_optimized_experiment(test_mode=True):
    """
    主实验流程 - 优化版本，支持2D输入到3D输出的零样本迁移
    Args:
        test_mode (bool): True=测试模式(少量数据), False=完整训练模式(全部数据)
    """
    # 首先进行分步调试
    if not debug_step_by_step(test_mode):
        print("❌ 分步调试失败，请检查模型架构和数据处理")
        return

    # 获取数据配置
    config = get_data_config(test_mode)

    print("\n" + "="*80)
    print("🎯 落锤试验预测 - 2D输入到3D输出零样本迁移实验")
    print("   - FinalBest: 纯2D输入(质量+高度) -> 3D输出(S,Pz,Yc)")
    print("   - 基线模型: 3D输入(质量+高度+时间) -> 3D输出")
    print("   - 零样本迁移能力，目标5%误差精度")
    print(f"   - {config['mode_name']}")
    print("="*80)

    # 1. 数据准备
    print("\n📊 数据准备阶段")
    data_ranges = compute_data_ranges()

    # 获取所有文件
    all_files = {}
    for data_dir, name in [(train_dir, 'train'), (test_dir, 'test'), (transfer_dir, 'transfer')]:
        if os.path.exists(data_dir):
            files = [f for f in os.listdir(data_dir) if f.endswith('.txt')]
            all_files[name] = files
            print(f"发现 {len(files)} 个{name}文件")
        else:
            all_files[name] = []

    # 数据分配 - 根据配置选择数据量
    all_train_files = all_files['train'] + all_files['transfer']
    all_test_files = all_files['test']

    # 应用数据量限制
    if config['max_train_files'] is not None:
        train_files = all_train_files[:config['max_train_files']]
    else:
        train_files = all_train_files

    if config['max_test_files'] is not None:
        test_files = all_test_files[:config['max_test_files']]
    else:
        test_files = all_test_files

    if config['max_val_files'] is not None:
        val_files = test_files[:config['max_val_files']]
    else:
        val_files = test_files[:len(test_files)//3]  # 使用1/3作为验证集

    print(f"✓ 使用 {len(train_files)} 个训练文件")
    print(f"✓ 使用 {len(test_files)} 个测试文件")
    print(f"✓ 使用 {len(val_files)} 个验证文件")

    # 2. 加载数据 - 为不同模型准备不同格式的数据
    print("\n🔄 加载和处理数据...")

    # 为每个模型准备数据
    model_data = {}

    for model_name in ['FinalBest', 'CNN', 'LSTM', 'Transformer']:
        print(f"  准备 {model_name} 数据...")

        train_data = []
        for filename in train_files:
            for data_dir in [train_dir, transfer_dir]:
                if os.path.exists(os.path.join(data_dir, filename)):
                    result = load_and_process_data_optimized(data_dir, filename, data_ranges, add_noise=True, model_type=model_name)
                    if result:
                        train_data.append(result)
                    break

        val_data = []
        for filename in val_files:
            result = load_and_process_data_optimized(test_dir, filename, data_ranges, add_noise=False, model_type=model_name)
            if result:
                val_data.append(result)

        test_data = []
        for filename in test_files:
            result = load_and_process_data_optimized(test_dir, filename, data_ranges, add_noise=False, model_type=model_name)
            if result:
                test_data.append(result)

        # 分离原始训练数据和迁移数据
        original_train_data = [d for d in train_data if 'Data_Train' in d.get('source_dir', '')]
        transfer_data = [d for d in train_data if 'Data_Transfer' in d.get('source_dir', '')]
        
        # 对原始训练数据和迁移数据分别进行不同强度的增强
        train_data_augmented = advanced_data_augmentation(
            original_train_data, 
            augment_factor=config['augment_factor_train']
        )
        transfer_data_augmented = advanced_data_augmentation(
            transfer_data, 
            augment_factor=config['augment_factor_transfer']
        )
        
        # 合并增强后的数据
        train_data_augmented = train_data_augmented + transfer_data_augmented

        model_data[model_name] = {
            'train': train_data_augmented,
            'val': val_data,
            'test': test_data
        }

    print(f"✓ 所有模型数据准备完成")

    # 3. 模型定义 - 使用增强特征输入模型
    models = {
        'FinalBest': FinalBestPredictor(seq_len=seq_length),  # 使用增强的5D输入模型
        'CNN': SimpleCNNPredictor(input_dim=6),  # 基线模型使用6D输入
        'LSTM': SimpleLSTMPredictor(input_dim=6, hidden_dim=256),
        'Transformer': SimpleTransformerPredictor(input_dim=6, d_model=256, nhead=8, num_layers=4)
    }

    # 4. 训练所有模型
    print("\n🎓 模型训练阶段")
    training_results = {}
    all_results = {}

    for model_name, model in models.items():
        print(f"\n--- 训练 {model_name} ---")

        param_count = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"模型参数量: {param_count:,}")

        # 清理GPU内存
        torch.cuda.empty_cache()

        # 获取对应模型的数据
        current_train_data = model_data[model_name]['train']
        current_val_data = model_data[model_name]['val']
        current_test_data = model_data[model_name]['test']

        # 训练模型
        train_losses, val_losses = train_optimized_model(
            model, current_train_data, current_val_data, model_name, config
        )
        training_results[model_name] = {
            'train_losses': train_losses,
            'val_losses': val_losses
        }

        # 加载最佳模型并评估
        try:
            model.load_state_dict(torch.load(f"models/{model_name}_optimized_best.pt"))
            print(f"✓ 加载最佳模型: {model_name}")
        except:
            print(f"⚠️ 无法加载最佳模型，使用当前模型: {model_name}")

        results = evaluate_optimized_model(model, current_test_data, model_name, data_ranges)
        all_results[model_name] = results

        # 计算平均指标
        if results:
            avg_metrics = {
                'mae_S_percent': np.mean([r['mae_S_percent'] for r in results]),
                'mae_Pz_percent': np.mean([r['mae_Pz_percent'] for r in results]),
                'mae_Yc_percent': np.mean([r['mae_Yc_percent'] for r in results]),
                'inference_time': np.mean([r['inference_time'] for r in results])
            }

            print(f"\n✅ {model_name} 性能:")
            print(f"  MAE_S: {avg_metrics['mae_S_percent']:.2f}%")
            print(f"  MAE_Pz: {avg_metrics['mae_Pz_percent']:.2f}%")
            print(f"  MAE_Yc: {avg_metrics['mae_Yc_percent']:.2f}%")
            print(f"  推理时间: {avg_metrics['inference_time']:.4f}s")

            # 判断目标达成
            s_target = avg_metrics['mae_S_percent'] < 5.0
            pz_target = avg_metrics['mae_Pz_percent'] < 5.0
            yc_target = avg_metrics['mae_Yc_percent'] < 5.0

            print(f"  目标达成: S{'✅' if s_target else '❌'} Pz{'✅' if pz_target else '❌'} Yc{'✅' if yc_target else '❌'}")

        # 清理GPU内存
        torch.cuda.empty_cache()

    # 6. 集成学习阶段
    print("\n🔗 集成学习阶段")
    ensemble_results = create_ensemble_predictions(models, model_data, data_ranges, all_results)
    if ensemble_results:
        all_results['Ensemble'] = ensemble_results

        # 计算集成模型的平均指标
        avg_metrics = {
            'mae_S_percent': np.mean([r['mae_S_percent'] for r in ensemble_results]),
            'mae_Pz_percent': np.mean([r['mae_Pz_percent'] for r in ensemble_results]),
            'mae_Yc_percent': np.mean([r['mae_Yc_percent'] for r in ensemble_results]),
            'inference_time': np.mean([r['inference_time'] for r in ensemble_results])
        }

        print(f"\n✅ Ensemble 性能:")
        print(f"  MAE_S: {avg_metrics['mae_S_percent']:.2f}%")
        print(f"  MAE_Pz: {avg_metrics['mae_Pz_percent']:.2f}%")
        print(f"  MAE_Yc: {avg_metrics['mae_Yc_percent']:.2f}%")
        print(f"  推理时间: {avg_metrics['inference_time']:.4f}s")

        # 判断目标达成
        s_target = avg_metrics['mae_S_percent'] < 5.0
        pz_target = avg_metrics['mae_Pz_percent'] < 5.0
        yc_target = avg_metrics['mae_Yc_percent'] < 5.0

        print(f"  目标达成: S{'✅' if s_target else '❌'} Pz{'✅' if pz_target else '❌'} Yc{'✅' if yc_target else '❌'}")

    # 7. 结果分析和可视化
    print("\n📈 结果分析阶段")

    # 创建可视化（包含集成模型）
    all_model_names = list(models.keys())
    if 'Ensemble' in all_results:
        all_model_names.append('Ensemble')
    create_optimized_visualizations(all_results, all_model_names)

    # 8. 生成性能排行榜（包含集成模型）
    print(f"\n🏆 模型性能排行榜:")
    print("="*80)

    summary_data = []
    for model_name in all_model_names:
        if model_name in all_results and all_results[model_name]:
            results = all_results[model_name]
            avg_s = np.mean([r['mae_S_percent'] for r in results])
            avg_pz = np.mean([r['mae_Pz_percent'] for r in results])
            avg_yc = np.mean([r['mae_Yc_percent'] for r in results])
            avg_time = np.mean([r['inference_time'] for r in results])

            summary_data.append({
                'Model': model_name,
                'S_Error(%)': f"{avg_s:.2f}",
                'Pz_Error(%)': f"{avg_pz:.2f}",
                'Yc_Error(%)': f"{avg_yc:.2f}",
                'Time(s)': f"{avg_time:.4f}",
                'Targets_Met': f"{sum([avg_s<5, avg_pz<5, avg_yc<5])}/3"
            })

    if summary_data:
        summary_df = pd.DataFrame(summary_data)
        print(summary_df.to_string(index=False))

        # 保存结果
        os.makedirs("results", exist_ok=True)
        summary_df.to_csv("results/optimized_performance_summary.csv", index=False)

        with open('results/optimized_complete_results.pkl', 'wb') as f:
            pickle.dump({
                'training_results': training_results,
                'evaluation_results': all_results,
                'data_ranges': data_ranges,
                'summary': summary_data
            }, f)

        # 找出最佳模型
        best_model_idx = 0
        best_score = float('inf')
        for i, data in enumerate(summary_data):
            score = float(data['S_Error(%)']) + float(data['Pz_Error(%)']) + float(data['Yc_Error(%)'])
            if score < best_score:
                best_score = score
                best_model_idx = i

        best_model = summary_data[best_model_idx]['Model']
        print(f"\n✅ 实验完成！")
        print(f"🥇 最佳模型: {best_model}")
        print(f"📊 综合误差: {best_score:.2f}%")
        print(f"📁 结果保存在 results/ 和 visualizations/ 目录")

        # 最终总结
        print(f"\n🌟 优化实验总结:")
        print(f"  ✅ 集成了train_final_best.py的最佳模型架构")
        print(f"  ✅ 采用了优化的训练策略（数据增强、学习率调度等）")
        print(f"  ✅ 保持了train_gai.py的输出格式和评估体系")
        print(f"  ✅ 训练速度提升（减少轮数，优化批处理）")
        print(f"  ✅ 提供了完整的模型对比和性能分析")
    else:
        print("❌ 没有有效的评估结果")

if __name__ == "__main__":
    try:
        print("🚀 开始2D输入到3D输出零样本迁移实验")
        print("📋 实验目标:")
        print("   1. 验证纯2D输入(质量+高度)到3D输出(S,Pz,Yc)的映射能力")
        print("   2. 实现零样本迁移，不依赖传感器数据")
        print("   3. 达到5%精度目标")
        print("   4. 保持模型架构基本不变")
        print("🔧 特性：分步调试 + 智能权重调整 + 优化训练参数")

        # 选择运行模式
        TEST_MODE = False  # True=测试模式(快速验证), False=完整训练模式(全部数据)

        print(f"\n🎯 运行模式: {'测试模式(快速验证)' if TEST_MODE else '完整训练模式(全部数据)'}")
        main_optimized_experiment(test_mode=TEST_MODE)
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()

