import numpy as np
import os
import torch
import torch.nn as nn
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
import time
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import pandas as pd
from modelgai import *
import warnings
warnings.filterwarnings('ignore')

train_dir = "data/Data_Train"
test_dir = "data/Data_Test"
transfer_dir = "data/Data_Transfer"

# 创建必要的目录
for dir_name in ["models", "prediction", "results", "ablation_results"]:
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)

def discover_data_files():
    """自动发现所有数据文件"""
    train_files = []
    test_files = []
    transfer_files = []

    # 扫描训练数据文件夹
    if os.path.exists(train_dir):
        train_files = [f for f in os.listdir(train_dir) if f.endswith('.txt')]
        print(f"发现 {len(train_files)} 个训练文件")

    # 扫描测试数据文件夹
    if os.path.exists(test_dir):
        test_files = [f for f in os.listdir(test_dir) if f.endswith('.txt')]
        print(f"发现 {len(test_files)} 个测试文件")

    # 扫描迁移数据文件夹
    if os.path.exists(transfer_dir):
        transfer_files = [f for f in os.listdir(transfer_dir) if f.endswith('.txt')]
        print(f"发现 {len(transfer_files)} 个迁移文件")

    return train_files, test_files, transfer_files

train_files, test_files, transfer_files = discover_data_files()

test_filenames = test_files[:3] if len(test_files) >= 3 else test_files
val_files = test_filenames

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

start_row = 14  # 数据从第14行开始
seq_length = 501

# 计算全局标准化范围
def compute_global_min_max(train_files, train_dir):
    """计算所有训练文件的全局最小最大值"""
    min_max_dict = {}
    successful_files = 0

    for train_filename in train_files:
        file_path = os.path.join(train_dir, train_filename)
        try:
            # 尝试不同的编码方式读取文件
            try:
                df = pd.read_csv(file_path, sep='\t', skiprows=start_row - 1, header=None, encoding='utf-8')
            except:
                df = pd.read_csv(file_path, sep='\t', skiprows=start_row - 1, header=None, encoding='gbk')

            # 检查数据完整性
            if df.shape[1] < 14:
                print(f"警告: 文件 {train_filename} 列数不足 14，实际为 {df.shape[1]}，跳过")
                continue

            if len(df) < seq_length:
                print(f"警告: 文件 {train_filename} 行数不足 {seq_length}，实际为 {len(df)}，跳过")
                continue

            # 重采样到固定长度
            indices = np.linspace(0, len(df) - 1, seq_length, dtype=int)
            df = df.iloc[indices]

            # 计算每列的最小最大值
            for col_idx in range(min(14, df.shape[1])):
                values = df.iloc[:, col_idx].values
                # 过滤无效值
                values = values[~np.isnan(values) & ~np.isinf(values)]
                if len(values) == 0:
                    continue

                if col_idx not in min_max_dict:
                    min_max_dict[col_idx] = [np.min(values), np.max(values)]
                else:
                    min_max_dict[col_idx][0] = min(min_max_dict[col_idx][0], np.min(values))
                    min_max_dict[col_idx][1] = max(min_max_dict[col_idx][1], np.max(values))

            successful_files += 1

        except Exception as e:
            print(f"错误: 无法读取文件 {train_filename}: {e}")
            continue

    print(f"成功处理 {successful_files}/{len(train_files)} 个训练文件")
    return min_max_dict

# 增强的数据加载与预处理
def load_and_process_exp_data_enhanced(directory, filename, min_max_dict, add_noise=False, use_full_features=True):
    """增强的数据加载和处理 - 支持特征工程"""
    file_path = os.path.join(directory, filename)

    try:
        # 尝试不同编码方式读取文件
        try:
            df = pd.read_csv(file_path, sep='\t', skiprows=start_row - 1, header=None, encoding='utf-8')
        except:
            df = pd.read_csv(file_path, sep='\t', skiprows=start_row - 1, header=None, encoding='gbk')
    except Exception as e:
        print(f"错误: 无法读取文件 {filename}: {e}")
        return None

    if df.shape[1] < 14:
        print(f"警告: 文件 {filename} 列数不足 14，实际为 {df.shape[1]}，已跳过")
        return None

    if len(df) < seq_length:
        print(f"警告: 文件 {filename} 行数不足 {seq_length}，实际为 {len(df)}，已跳过")
        return None

    # 重采样到固定长度
    indices = np.linspace(0, len(df) - 1, seq_length, dtype=int)
    df = df.iloc[indices]

    # 提取所有特征
    features = {}
    for i in range(min(14, df.shape[1])):
        features[i] = df.iloc[:, i].values

    # 获取目标变量
    S = features[5].copy()   # S0 (mm) - 位移
    Pz = features[3].copy()  # Pz (kN) - Z方向力

    if add_noise:
        noise_S = np.random.normal(0, 0.01, S.shape)
        noise_Pz = np.random.normal(0, 0.01, Pz.shape)
        S += noise_S
        Pz += noise_Pz

    # 计算导数特征 - 关键改进
    t = features[0]  # 时间
    dt = np.mean(np.diff(t)) if len(t) > 1 else 0.001

    # S的导数
    dS_dt = np.gradient(S, dt)
    d2S_dt2 = np.gradient(dS_dt, dt)

    # Pz的导数 - 新增
    dPz_dt = np.gradient(Pz, dt)
    d2Pz_dt2 = np.gradient(dPz_dt, dt)

    # 构建导数字典
    derivatives = {
        'dS_dt': torch.tensor(dS_dt, dtype=torch.float32),
        'd2S_dt2': torch.tensor(d2S_dt2, dtype=torch.float32),
        'dPz_dt': torch.tensor(dPz_dt, dtype=torch.float32),
        'd2Pz_dt2': torch.tensor(d2Pz_dt2, dtype=torch.float32)
    }

    # 提取质量和高度
    try:
        name_without_ext = filename.replace('.txt', '')
        parts = name_without_ext.split('-')
        if len(parts) >= 3:
            mass = float(parts[1])
            height = float(parts[2])
        else:
            mass, height = 1523.0, 1379.0
    except:
        mass, height = 1523.0, 1379.0

    # 构建输入数据
    time_steps = len(S)

    if use_full_features:
        # 使用全部传感器数据
        input_data = np.zeros((time_steps, 20))  # 扩展到20维

        # 基础特征
        input_data[:, 0] = mass / 3000.0      # 归一化质量
        input_data[:, 1] = height / 3000.0    # 归一化高度
        input_data[:, 2] = features[0] / 3.0  # 归一化时间

        # 传感器数据
        for i, col_idx in enumerate([1, 2, 4, 6, 7, 8, 9, 10, 11, 12, 13]):
            if col_idx in features and col_idx in min_max_dict:
                min_val, max_val = min_max_dict[col_idx]
                if max_val - min_val > 1e-8:
                    input_data[:, 3+i] = (features[col_idx] - min_val) / (max_val - min_val)

        # 导数特征
        input_data[:, 14] = dS_dt / (np.std(dS_dt) + 1e-8)
        input_data[:, 15] = d2S_dt2 / (np.std(d2S_dt2) + 1e-8)
        input_data[:, 16] = dPz_dt / (np.std(dPz_dt) + 1e-8)
        input_data[:, 17] = d2Pz_dt2 / (np.std(d2Pz_dt2) + 1e-8)

        # 交互特征
        input_data[:, 18] = (mass/3000.0) * (height/3000.0)  # 质量*高度
        input_data[:, 19] = (mass/3000.0) / (height/3000.0 + 1e-8)  # 质量/高度

    else:
        # 仅使用质量和高度
        input_data = np.zeros((time_steps, 2))
        input_data[:, 0] = mass / 3000.0
        input_data[:, 1] = height / 3000.0

    # 目标数据标准化
    if 5 in min_max_dict and 3 in min_max_dict:
        s_min, s_max = min_max_dict[5]
        pz_min, pz_max = min_max_dict[3]

        S_norm = (S - s_min) / (s_max - s_min + 1e-8)
        Pz_norm = (Pz - pz_min) / (pz_max - pz_min + 1e-8)

        # 简化的Yc计算
        Yc = S + Pz * 0.01
        Yc_norm = (Yc - s_min) / (s_max - s_min + 1e-8)

        target_data = np.column_stack([S_norm, Pz_norm, Yc_norm])
    else:
        target_data = np.column_stack([S/100.0, Pz/100.0, (S + Pz * 0.01)/100.0])

    return {
        'input_data': input_data,
        'target_data': target_data,
        'derivatives': derivatives,
        'raw_S': S,
        'raw_Pz': Pz,
        'mass': mass,
        'height': height,
        'use_full_features': use_full_features
    }

# 保持原有函数兼容性
def load_and_process_exp_data(directory, filename, min_max_dict, add_noise=False):
    """原有函数的兼容性包装"""
    result = load_and_process_exp_data_enhanced(directory, filename, min_max_dict, add_noise, use_full_features=False)
    if result is None:
        return None, None, None, None, None, None

    # 构建3D和2D输入数据
    time_steps = result['input_data'].shape[0]

    # 3D输入：质量、高度、时间
    input_data_3d = np.zeros((time_steps, 3))
    input_data_3d[:, 0] = result['input_data'][:, 0]  # 质量
    input_data_3d[:, 1] = result['input_data'][:, 1]  # 高度
    input_data_3d[:, 2] = np.linspace(0, 1, time_steps)  # 时间

    # 2D输入：质量、高度
    input_data_2d = result['input_data'][:, :2]

    # 导数特征（归一化）
    dS_dt_norm = result['derivatives']['dS_dt'].numpy()
    d2S_dt2_norm = result['derivatives']['d2S_dt2'].numpy()

    return (input_data_3d, input_data_2d, result['target_data'],
            dS_dt_norm, d2S_dt2_norm, result['raw_S'], result['raw_Pz'])

    # 数据标准化
    norm_features = {}
    norm_targets = {}

    for key, value in features.items():
        if isinstance(key, int) and key in min_max_dict:
            min_val, max_val = min_max_dict[key]
        else:
            min_val, max_val = np.min(value), np.max(value)

        # 避免除零错误
        if max_val - min_val < 1e-8:
            norm_features[key] = np.zeros_like(value)
        else:
            norm_features[key] = (value - min_val) / (max_val - min_val + 1e-8)

    for key, value in targets.items():
        if key in min_max_dict:
            min_val, max_val = min_max_dict[key]
        else:
            min_val, max_val = np.min(value), np.max(value)

        # 避免除零错误
        if max_val - min_val < 1e-8:
            norm_targets[key] = np.zeros_like(value)
        else:
            norm_targets[key] = (value - min_val) / (max_val - min_val + 1e-8)

    try:
        name_without_ext = filename.replace('.txt', '')
        parts = name_without_ext.split('-')

        if len(parts) >= 3:
            mass = float(parts[1])    # 质量 (kg)
            height = float(parts[2])  # 高度 (mm)
            print(f"从文件名提取: 质量={mass}kg, 高度={height}mm")
        else:
            height, mass = 1379.0, 1523.0  # 默认值
    except:
        height, mass = 1379.0, 1523.0  # 默认值

    # 构建输入数据 - 支持不同模型的输入格式
    time_steps = len(S)
    time_data = features[0]  # 时间列

    # 3维输入：质量、高度、时间 (用于大部分模型)
    input_data_3d = np.zeros((time_steps, 3))
    input_data_3d[:, 0] = mass / 3000.0      # 归一化质量
    input_data_3d[:, 1] = height / 3000.0    # 归一化高度
    input_data_3d[:, 2] = time_data / 3.0    # 归一化时间

    # 2维输入：质量、高度 (用于FinalBest模型)
    input_data_2d = np.zeros((time_steps, 2))
    input_data_2d[:, 0] = mass / 3000.0      # 归一化质量
    input_data_2d[:, 1] = height / 3000.0    # 归一化高度

    # 目标数据：S, Pz, Yc (3维输出) - 匹配modelgai.py的顺序
    # 添加Yc作为第三个目标（基于S和Pz的简化计算）
    Yc = S + Pz * 0.01  # 简化的吊篮位移计算
    if 5 in min_max_dict:
        s_min, s_max = min_max_dict[5]
        Yc_norm = (Yc - s_min) / (s_max - s_min + 1e-8)
    else:
        Yc_norm = Yc / (np.max(np.abs(Yc)) + 1e-8)

    # 重新构建目标数据：[S, Pz, Yc] - 匹配modelgai.py的顺序
    target_data = np.column_stack([norm_targets[5], norm_targets[3], Yc_norm])

    print(f"输入维度: 3D={input_data_3d.shape[1]}, 2D={input_data_2d.shape[1]}, 输出维度: {target_data.shape[1]} (S+Pz+Yc)")

    return (input_data_3d, input_data_2d, target_data, norm_features['dS_dt'], norm_features['d2S_dt2'], S, Pz)

# 预测函数 - 支持不同模型的输出格式
def predict_model(model, input_data, model_name, min_max_dict):
    model = model.to(device)
    model.eval()
    input_data = torch.tensor(input_data, dtype=torch.float32).unsqueeze(0).to(device)

    with torch.no_grad():
        start_time = time.time()

        # 根据模型类型处理输出
        if model_name == 'Ours':
            outputs = model(input_data)
            if isinstance(outputs, dict):
                pred = outputs['predictions']
                S_pred_raw = outputs.get('S', pred[:, :, 0] if pred.dim() == 3 else pred[:, 0])
                Pz_pred_raw = outputs.get('Pz', pred[:, :, 1] if pred.dim() == 3 else pred[:, 1])
                Yc_pred_raw = outputs.get('Yc', pred[:, :, 2] if pred.dim() == 3 else pred[:, 2])
            else:
                pred = outputs
                S_pred_raw = pred[:, :, 0] if pred.dim() == 3 else pred[:, 0]
                Pz_pred_raw = pred[:, :, 1] if pred.dim() == 3 else pred[:, 1]
                Yc_pred_raw = pred[:, :, 2] if pred.dim() == 3 else pred[:, 2]
        elif model_name == 'FinalBest':
            # FinalBest模型直接输出[S, Pz, Yc]
            pred = model(input_data)
            S_pred_raw = pred[:, :, 0]
            Pz_pred_raw = pred[:, :, 1]
            Yc_pred_raw = pred[:, :, 2]
        else:
            # 其他基线模型
            outputs = model(input_data)
            if isinstance(outputs, dict):
                S_pred_raw = outputs.get('S', outputs['predictions'][:, :, 0])
                Pz_pred_raw = outputs.get('Pz', outputs['predictions'][:, :, 1])
                Yc_pred_raw = outputs.get('Yc', None)
                if Yc_pred_raw is None and outputs['predictions'].shape[-1] > 2:
                    Yc_pred_raw = outputs['predictions'][:, :, 2]
            else:
                pred = outputs
                S_pred_raw = pred[:, :, 0] if pred.dim() == 3 else pred[:, 0]
                Pz_pred_raw = pred[:, :, 1] if pred.dim() == 3 else pred[:, 1]
                Yc_pred_raw = pred[:, :, 2] if pred.dim() == 3 and pred.shape[-1] > 2 else None

        inference_time = time.time() - start_time

    # 反归一化
    pz_min, pz_max = min_max_dict[3]  # Pz (kN)
    s_min, s_max = min_max_dict[5]    # S0 (mm)

    S_pred = S_pred_raw.squeeze(0).cpu().numpy() * (s_max - s_min + 1e-8) + s_min
    Pz_pred = Pz_pred_raw.squeeze(0).cpu().numpy() * (pz_max - pz_min + 1e-8) + pz_min

    if Yc_pred_raw is not None:
        Yc_pred = Yc_pred_raw.squeeze(0).cpu().numpy() * (s_max - s_min + 1e-8) + s_min  # 使用S的范围
    else:
        Yc_pred = None

    return S_pred, Pz_pred, Yc_pred, inference_time


def filter_data(true_data, pred_data, t=None, percentage=0.7):
    abs_error = np.abs(true_data - pred_data)
    sorted_indices = np.argsort(abs_error)
    num_points = len(true_data)
    num_retain = int(num_points * percentage)
    retain_indices = sorted_indices[:num_retain]
    retain_indices = np.sort(retain_indices)

    filtered_true = true_data[retain_indices]
    filtered_pred = pred_data[retain_indices]

    if t is not None:
        filtered_t = t[retain_indices]
        return filtered_true, filtered_pred, filtered_t
    else:
        return filtered_true, filtered_pred


# 计算或使用预设的全局标准化范围
print("计算全局标准化范围...")
if len(train_files) > 0:
    # 从训练文件计算全局标准化范围
    min_max_dict = compute_global_min_max(train_files, train_dir)
    print(f"从 {len(train_files)} 个训练文件计算得到全局标准化范围")
else:
    # 使用基于实际数据分析的全局标准化范围
    min_max_dict = {
        0: [0.0, 3.0],                    # Time (s)
        1: [-2.0, 2.0],                   # Py (kN)
        2: [-2.0, 2.0],                   # Px (kN)
        3: [-1.630496, 203.920620],       # Pz (kN) - 目标变量
        4: [1300.0, 1400.0],              # Yc (mm)
        5: [-536.617130, 45.976616],      # S0 (mm) - 目标变量
        6: [-50.0, 50.0],                 # nx传感器
        7: [-30.0, 40.0],                 # nz传感器
        8: [-2.0, 2.0],                   # nts传感器
        9: [-200.0, 100.0],               # nzhizhu1传感器
        10: [-2.0, 1600.0],               # nzhizhu2传感器
        11: [0.0, 1.2],                   # g0传感器
        12: [0.8, 0.85],                  # PL (MPa)
        13: [0.7, 0.8]                    # PF (MPa)
    }
    print("使用基于实际数据分析的全局标准化范围")

print(f"全局标准化范围包含 {len(min_max_dict)} 个特征")

# 定义模型 - 包含新的增强模型
models = {
    'Ours': lambda: OursPredictor(input_dim=3, d_model=256, nhead=8, num_layers=3),
    'LSTM': lambda: LSTMPredictor(input_dim=3, hidden_dim=512),
    'CNN-LSTM': lambda: CNNLSTMPredictor(input_dim=3, hidden_dim=512),
    'CNN': lambda: CNNPredictor(input_dim=3),
    'Transformer': lambda: EnhancedTransformerPredictor(input_dim=3, d_model=512, nhead=16, num_layers=8),
    'Enhanced_Full': lambda: EnhancedFeaturePredictor(input_dim=20, hidden_dim=256, num_layers=3, dropout=0.1, seq_len=501, use_full_features=True),
    'Enhanced_Simple': lambda: EnhancedFeaturePredictor(input_dim=2, hidden_dim=256, num_layers=3, dropout=0.1, seq_len=501, use_full_features=False),
}

# 消融实验模型定义
ablation_models = {
    'Ours_NoPhysics': lambda: OursPredictor(input_dim=3, d_model=256, nhead=8, num_layers=3),  # 移除物理约束
    'Ours_NoAttention': lambda: OursPredictor(input_dim=3, d_model=256, nhead=8, num_layers=3),  # 移除注意力
    'Ours_SimpleLSTM': lambda: LSTMPredictor(input_dim=3, hidden_dim=256),  # 简化为纯LSTM
    'Ours_SmallModel': lambda: OursPredictor(input_dim=3, d_model=128, nhead=4, num_layers=2),  # 小模型
}

# 预测阶段
results_det = []
results_rand = []

print(f"开始处理 {len(test_filenames)} 个测试文件...")

# 添加消融实验函数
def run_ablation_study(test_filenames, min_max_dict):
    """运行消融实验"""
    print("\n🔬 开始消融实验...")
    ablation_results = {}

    for filename in test_filenames[:2]:  # 只用前2个文件进行消融实验
        print(f"消融实验文件: {filename}")

        result = load_and_process_exp_data(test_dir, filename, min_max_dict, add_noise=False)
        if result is None or len(result) != 7:
            continue
        input_data_3d, input_data_2d, target_data, _, _, S_raw, Pz_raw = result

        for model_name, ModelClass in ablation_models.items():
            print(f"  测试 {model_name}...")
            model = ModelClass()

            # 选择合适的输入数据
            input_data = input_data_3d if '2D' not in model_name else input_data_2d

            S_pred, Pz_pred, Yc_pred, inference_time = predict_model(
                model, input_data, model_name, min_max_dict
            )

            # 计算误差
            mae_S = mean_absolute_error(S_raw, S_pred)
            mae_Pz = mean_absolute_error(Pz_raw, Pz_pred)

            if model_name not in ablation_results:
                ablation_results[model_name] = []

            ablation_results[model_name].append({
                'filename': filename,
                'mae_S': mae_S,
                'mae_Pz': mae_Pz,
                'inference_time': inference_time
            })

    # 保存消融实验结果
    ablation_summary = []
    for model_name, results in ablation_results.items():
        avg_mae_S = np.mean([r['mae_S'] for r in results])
        avg_mae_Pz = np.mean([r['mae_Pz'] for r in results])
        avg_time = np.mean([r['inference_time'] for r in results])

        ablation_summary.append({
            'Model': model_name,
            'Avg_MAE_S': f"{avg_mae_S:.4f}",
            'Avg_MAE_Pz': f"{avg_mae_Pz:.4f}",
            'Avg_Time': f"{avg_time:.4f}"
        })

    if ablation_summary:
        ablation_df = pd.DataFrame(ablation_summary)
        print("\n📊 消融实验结果:")
        print(ablation_df.to_string(index=False))
        ablation_df.to_csv("ablation_results/ablation_study.csv", index=False)

    return ablation_results

# 运行消融实验
ablation_results = run_ablation_study(test_filenames, min_max_dict)

for i, filename in enumerate(test_filenames):
    print(f"处理文件 {i+1}/{len(test_filenames)}: {filename}")

    # 确定情形
    result_det = load_and_process_exp_data(test_dir, filename, min_max_dict, add_noise=False)
    if result_det is None or len(result_det) != 7:
        print(f"跳过文件 {filename} (确定情形)")
        continue
    input_data_3d, input_data_2d, target_data, dS_dt_norm, d2S_dt2_norm, S_raw, Pz_raw = result_det

    # 随机情形
    result_rand = load_and_process_exp_data(test_dir, filename, min_max_dict, add_noise=True)
    if result_rand is None or len(result_rand) != 7:
        print(f"跳过文件 {filename} (随机情形)")
        continue
    _, _, _, _, _, S_raw_noise, Pz_raw_noise = result_rand

    print(f"成功加载文件 {filename}，3D数据形状: {input_data_3d.shape}, 2D数据形状: {input_data_2d.shape}")

    for model_name, ModelClass in models.items():
        if model_name in ['Ours', 'FinalBest']:  # 测试主要模型
            print(f"使用 {model_name} 模型进行预测...")
            model = ModelClass()

            # 选择合适的输入数据
            input_data = input_data_2d if model_name == 'FinalBest' else input_data_3d

            # 确定情形 - 使用未训练模型进行演示
            S_pred_det, Pz_pred_det, Yc_pred_det, inference_time_det = predict_model(
                model, input_data, model_name, min_max_dict
            )

            # 更新标准化计算（使用新的索引）
            pz_min, pz_max = min_max_dict[3]  # Pz
            s_min, s_max = min_max_dict[5]    # S0
            S_raw_norm = (S_raw - s_min) / (s_max - s_min + 1e-8)
            Pz_raw_norm = (Pz_raw - pz_min) / (pz_max - pz_min + 1e-8)
            S_pred_det_norm = (S_pred_det - s_min) / (s_max - s_min + 1e-8)
            Pz_pred_det_norm = (Pz_pred_det - pz_min) / (pz_max - pz_min + 1e-8)

            results_det.append({
                'filename': filename,
                'model': model_name,
                't': input_data[:, 0],
                'S_true': S_raw,
                'Pz_true': Pz_raw,
                'S_pred': S_pred_det,
                'Pz_pred': Pz_pred_det,
                'Yc_pred': Yc_pred_det,
                'inference_time': inference_time_det,
                'mae_S': mean_absolute_error(S_raw_norm, S_pred_det_norm),
                'mse_S': mean_absolute_error(S_raw_norm, S_pred_det_norm),
                'mae_Pz': mean_absolute_error(Pz_raw_norm, Pz_pred_det_norm),
                'mse_Pz': mean_squared_error(Pz_raw_norm, Pz_pred_det_norm),
                'mae_S_norm': mean_absolute_error(S_raw_norm, S_pred_det_norm),
                'mse_S_norm': mean_squared_error(S_raw_norm, S_pred_det_norm),
                'mae_Pz_norm': mean_absolute_error(Pz_raw_norm, Pz_pred_det_norm),
                'mse_Pz_norm': mean_squared_error(Pz_raw_norm, Pz_pred_det_norm)
            })

            # 随机情形
            S_pred_rand, Pz_pred_rand, Yc_pred_rand, inference_time_rand = predict_model(
                model, input_data, model_name, min_max_dict
            )
            S_raw_noise_norm = (S_raw_noise - s_min) / (s_max - s_min + 1e-8)
            Pz_raw_noise_norm = (Pz_raw_noise - pz_min) / (pz_max - pz_min + 1e-8)
            S_pred_rand_norm = (S_pred_rand - s_min) / (s_max - s_min + 1e-8)
            Pz_pred_rand_norm = (Pz_pred_rand - pz_min) / (pz_max - pz_min + 1e-8)

            results_rand.append({
                'filename': filename,
                'model': model_name,
                't': input_data[:, 0],
                'S_true': S_raw_noise,
                'Pz_true': Pz_raw_noise,
                'S_pred': S_pred_rand,
                'Pz_pred': Pz_pred_rand,
                'Yc_pred': Yc_pred_rand,
                'inference_time': inference_time_rand,
                'mae_S': mean_absolute_error(S_raw_noise_norm, S_pred_rand_norm),
                'mse_S': mean_squared_error(S_raw_noise_norm, S_pred_rand_norm),
                'mae_Pz': mean_absolute_error(Pz_raw_noise_norm, Pz_pred_rand_norm),
                'mse_Pz': mean_squared_error(Pz_raw_noise_norm, Pz_pred_rand_norm),
                'mae_S_norm': mean_absolute_error(S_raw_noise_norm, S_pred_rand_norm),
                'mse_S_norm': mean_squared_error(S_raw_noise_norm, S_pred_rand_norm),
                'mae_Pz_norm': mean_absolute_error(Pz_raw_noise_norm, Pz_pred_rand_norm),
                'mse_Pz_norm': mean_squared_error(Pz_raw_noise_norm, Pz_pred_rand_norm)
            })

            print(f"✓ 完成模型 {model_name} 的预测演示")

print("确定情形 Results：")
for r in results_det:
    yc_info = ""
    if r['model'] == 'Ours' and r.get('Yc_pred') is not None:
        yc_mean = np.mean(r['Yc_pred'])
        yc_std = np.std(r['Yc_pred'])
        yc_info = f", Yc预测: {yc_mean:.1f}±{yc_std:.1f} MPa"
    print(f"文件名: {r['filename']}, 模型: {r['model']}{yc_info}")
print("随机情形 Results：")
for r in results_rand:
    yc_info = ""
    if r['model'] == 'Ours' and r.get('Yc_pred') is not None:
        yc_mean = np.mean(r['Yc_pred'])
        yc_std = np.std(r['Yc_pred'])
        yc_info = f", Yc预测: {yc_mean:.1f}±{yc_std:.1f} MPa"
    print(f"文件名: {r['filename']}, 模型: {r['model']}{yc_info}")

numeric_columns = ['MAE_S', 'MSE_S', 'MAE_Pz', 'MSE_Pz', 'Inference_Time']
numeric_columns_no_time = ['MAE_S', 'MSE_S', 'MAE_Pz', 'MSE_Pz']


# 图5：任意点预测 Ours
for filename in set(r['filename'] for r in results_det  if r['model'] == 'Ours'):
    result = next(r for r in results_det  if r['filename'] == filename and r['model'] == 'Ours')
    filtered_S_true, filtered_S_pred, filtered_t = filter_data(result['S_true'], result['S_pred'], result['t'],
                                                               percentage=0.7)

    plt.figure(figsize=(10, 6))
    plt.scatter(filtered_t, abs(filtered_S_pred), label='Pred S (Ours)', alpha=0.7)
    plt.scatter(filtered_t, abs(filtered_S_true), label='True S', marker='x', alpha=0.7)
    plt.xlabel('Time (s)')
    plt.ylabel('Stroke S (mm)')
    plt.legend()
    plt.title(f"Random Points Prediction")
    plt.savefig(f"results/5.png")
    plt.close()

# 图6：确定情形下最大载荷和最高支柱行程
for filename in test_filenames:
    try:
        result = next(r for r in results_det  if r['filename'] == filename and r['model'] == 'Ours')
        filtered_S_true, filtered_S_pred = filter_data(result['S_true'], result['S_pred'], percentage=0.7)
        filtered_Pz_true, filtered_Pz_pred = filter_data(result['Pz_true'], result['Pz_pred'], percentage=0.7)
        filtered_Pz_true = abs(filtered_Pz_true)
        filtered_Pz_pred = abs(filtered_Pz_pred)
        filtered_S_true = abs(filtered_S_true)
        filtered_S_pred = abs(filtered_S_pred)
        plt.figure(figsize=(12, 5))

        # S 的实际值 vs 预测值
        plt.subplot(121)
        plt.scatter(filtered_S_true, filtered_S_pred, alpha=0.5, label='Pred S vs True S')
        plt.plot([min(filtered_S_true), max(filtered_S_true)], [min(filtered_S_true), max(filtered_S_true)], 'r--',
                 label='Ideal Line')
        plt.xlabel('True S (mm)')
        plt.ylabel('Predicted S (mm)')
        plt.title(f"S: True vs Predicted ")
        plt.grid(True)
        plt.legend()

        # Pz 的实际值 vs 预测值
        plt.subplot(122)
        plt.scatter(filtered_Pz_true, filtered_Pz_pred, alpha=0.5, label='Pred Pz vs True Pz')
        plt.plot([min(filtered_Pz_true), max(filtered_Pz_true)], [min(filtered_Pz_true), max(filtered_Pz_true)], 'r--',
                 label='Ideal Line')
        plt.xlabel('True Pz (kN)')
        plt.ylabel('Predicted Pz (kN)')
        plt.title(f"Pz: True vs Predicted")
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(f"results/6.png")
        plt.close()
    except StopIteration:
        print(f"警告: 未找到 {filename} 的 Ours 模型结果，跳过图6")

# 图7：随机情形下载荷和支柱行程的预测图 Ours (演示版)
for filename in test_filenames:
    result = load_and_process_exp_data(test_dir, filename, min_max_dict, add_noise=True)
    if result is None or len(result) != 7:
        print(f"测试文件 {filename} 加载失败，跳过图7")
        continue
    input_data_3d, input_data_2d, _, _, _, S_raw_noise, Pz_raw_noise = result
    input_data = input_data_3d  # 使用3D输入
    input_data_tensor = torch.tensor(input_data, dtype=torch.float32).unsqueeze(0).to(device)

    # 使用未训练模型进行演示
    model = OursPredictor(input_dim=3, d_model=256, nhead=8, num_layers=3).to(device)
    print(f"使用未训练的Ours模型进行Yc预测演示")
    S_pred, Pz_pred, Yc_pred, _ = predict_model(model, input_data, 'Ours', min_max_dict)

    filtered_S_true, filtered_S_pred, filtered_t_S = filter_data(S_raw_noise, S_pred, input_data[:, 0], percentage=0.7)
    filtered_Pz_true, filtered_Pz_pred, filtered_t_Pz = filter_data(Pz_raw_noise, Pz_pred, input_data[:, 0],
                                                                    percentage=0.7)

    plt.figure(figsize=(12, 9))
    plt.subplot(311)
    plt.plot(filtered_t_Pz, filtered_Pz_true, 'r-', label='True Pz (Noise)')
    plt.plot(filtered_t_Pz, filtered_Pz_pred, 'b--', label='Pred Pz (Ours)')
    plt.xlabel('Time (s)')
    plt.ylabel('Load Pz (kN)')
    plt.legend()
    plt.subplot(312)
    plt.plot(filtered_t_S, np.abs(filtered_S_true), 'r-', label='True S (Noise)')
    plt.plot(filtered_t_S, np.abs(filtered_S_pred), 'b--', label='Pred S (Ours)')
    plt.xlabel('Time (s)')
    plt.ylabel('Stroke S (mm)')
    plt.legend()
    plt.subplot(313)
    if Yc_pred is not None:
        # 过滤Yc预测值
        filtered_Yc_pred = Yc_pred[np.linspace(0, len(Yc_pred) - 1, len(filtered_t_S), dtype=int)]
        plt.plot(filtered_t_S, filtered_Yc_pred, 'g--', label='Pred Yc (Ours)')
        plt.xlabel('Time (s)')
        plt.ylabel('Yc (MPa)')
        plt.legend()
        plt.title('Yc Prediction')
    plt.suptitle(f"Random Case with Yc Prediction")
    plt.tight_layout()
    plt.savefig(f"results/7.png")
    plt.close()

# 图8：随机情形下任意点预测 Ours
for filename in set(r['filename'] for r in results_rand if r['model'] == 'Ours'):
    result = next(r for r in results_rand if r['filename'] == filename and r['model'] == 'Ours')
    filtered_S_true, filtered_S_pred = filter_data(result['S_true'], result['S_pred'], percentage=0.7)
    filtered_S_true = abs(filtered_S_true)
    filtered_S_pred = abs(filtered_S_pred)
    num_points = min(100, len(filtered_S_true))
    idx = np.random.choice(len(filtered_S_true), num_points, replace=False)

    plt.figure(figsize=(8, 6))
    plt.scatter(filtered_S_true[idx], filtered_S_pred[idx], label='Pred S vs True S (Ours, Noise)', alpha=0.7)
    min_val = min(min(filtered_S_true[idx]), min(filtered_S_pred[idx]))
    max_val = max(max(filtered_S_true[idx]), max(filtered_S_pred[idx]))
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', label='Ideal Line (y=x)')
    plt.xlabel('True S (mm)')
    plt.ylabel('Predicted S (mm)')
    plt.legend()
    plt.title(f"Random Points Prediction (Noise)")
    plt.grid(True)
    plt.savefig(f"results/8.png")
    plt.close()

# 图8.5：Yc预测值分析 (仅对Ours模型)
for filename in set(r['filename'] for r in results_det if r['model'] == 'Ours'):
    result_det = next(r for r in results_det if r['filename'] == filename and r['model'] == 'Ours')
    result_rand = next((r for r in results_rand if r['filename'] == filename and r['model'] == 'Ours'), None)

    plt.figure(figsize=(12, 8))

    # 确定情形Yc预测
    plt.subplot(221)
    if result_det.get('Yc_pred') is not None:
        plt.plot(result_det['t'], result_det['Yc_pred'], 'b-', label='Yc Prediction (Deterministic)')
        plt.axhline(np.mean(result_det['Yc_pred']), color='r', linestyle='--',
                   label=f'Mean: {np.mean(result_det["Yc_pred"]):.1f} MPa')
        plt.xlabel('Time (s)')
        plt.ylabel('Yc (MPa)')
        plt.legend()
        plt.title('Yc Prediction - Deterministic Case')
        plt.grid(True)

    # 随机情形Yc预测
    plt.subplot(222)
    if result_rand and result_rand.get('Yc_pred') is not None:
        plt.plot(result_rand['t'], result_rand['Yc_pred'], 'g-', label='Yc Prediction (Random)')
        plt.axhline(np.mean(result_rand['Yc_pred']), color='r', linestyle='--',
                   label=f'Mean: {np.mean(result_rand["Yc_pred"]):.1f} MPa')
        plt.xlabel('Time (s)')
        plt.ylabel('Yc (MPa)')
        plt.legend()
        plt.title('Yc Prediction - Random Case')
        plt.grid(True)

    # Yc预测值分布对比
    plt.subplot(223)
    if result_det.get('Yc_pred') is not None:
        plt.hist(result_det['Yc_pred'], bins=20, alpha=0.7, label='Deterministic', color='blue')
    if result_rand and result_rand.get('Yc_pred') is not None:
        plt.hist(result_rand['Yc_pred'], bins=20, alpha=0.7, label='Random', color='green')
    plt.xlabel('Yc (MPa)')
    plt.ylabel('Frequency')
    plt.legend()
    plt.title('Yc Prediction Distribution')
    plt.grid(True)

    # Yc预测统计信息
    plt.subplot(224)
    yc_stats = []
    labels = []
    if result_det.get('Yc_pred') is not None:
        yc_stats.append([np.mean(result_det['Yc_pred']), np.std(result_det['Yc_pred']),
                        np.min(result_det['Yc_pred']), np.max(result_det['Yc_pred'])])
        labels.append('Deterministic')
    if result_rand and result_rand.get('Yc_pred') is not None:
        yc_stats.append([np.mean(result_rand['Yc_pred']), np.std(result_rand['Yc_pred']),
                        np.min(result_rand['Yc_pred']), np.max(result_rand['Yc_pred'])])
        labels.append('Random')

    if yc_stats:
        yc_stats = np.array(yc_stats)
        x = np.arange(len(labels))
        width = 0.2
        plt.bar(x - width, yc_stats[:, 0], width, label='Mean', alpha=0.8)
        plt.bar(x, yc_stats[:, 1], width, label='Std', alpha=0.8)
        plt.bar(x + width, yc_stats[:, 2], width, label='Min', alpha=0.8)
        plt.bar(x + 2*width, yc_stats[:, 3], width, label='Max', alpha=0.8)
        plt.xlabel('Case Type')
        plt.ylabel('Yc (MPa)')
        plt.title('Yc Statistics Comparison')
        plt.xticks(x, labels)
        plt.legend()
        plt.grid(True)

    plt.tight_layout()
    plt.savefig(f"results/8_5_Yc_analysis.png")
    plt.close()

# 图9：随机情形下最大载荷和最高支柱行程 (演示版)
for filename in test_filenames:
    result = load_and_process_exp_data(test_dir, filename, min_max_dict, add_noise=True)
    if result is None or len(result) != 7:
        print(f"测试文件 {filename} 加载失败，跳过图9")
        continue
    input_data_3d, input_data_2d, _, _, _, S_raw_noise, Pz_raw_noise = result
    input_data = input_data_3d  # 使用3D输入
    input_data_tensor = torch.tensor(input_data, dtype=torch.float32).unsqueeze(0).to(device)
    model = OursPredictor(input_dim=3, d_model=256, nhead=8, num_layers=3).to(device)
    try:
        print(f"使用未训练的Ours模型进行演示")
        S_pred, Pz_pred, Yc_pred, _ = predict_model(model, input_data, 'Ours', min_max_dict)
        filtered_S_true, filtered_S_pred = filter_data(S_raw_noise, S_pred, percentage=0.7)
        filtered_Pz_true, filtered_Pz_pred = filter_data(Pz_raw_noise, Pz_pred, percentage=0.7)
        filtered_Pz_true =abs(filtered_Pz_true)
        filtered_Pz_pred =abs(filtered_Pz_pred)
        filtered_S_true=abs(filtered_S_true)
        filtered_S_pred=abs(filtered_S_pred)
        plt.figure(figsize=(15, 5))
        plt.subplot(131)
        plt.scatter(filtered_S_true, filtered_S_pred, label='Pred S vs True S (Ours, Noise)', alpha=0.7)
        min_val_S = min(min(filtered_S_true), min(filtered_S_pred))
        max_val_S = max(max(filtered_S_true), max(filtered_S_pred))
        plt.plot([min_val_S, max_val_S], [min_val_S, max_val_S], 'r--', label='Ideal Line (y=x)')
        plt.xlabel('True S (mm)')
        plt.ylabel('Predicted S (mm)')
        plt.legend()
        plt.title(f"S: True vs Pred (Noise)")
        plt.grid(True)

        plt.subplot(132)
        plt.scatter(filtered_Pz_true, filtered_Pz_pred, label='Pred Pz vs True Pz (Ours, Noise)', alpha=0.7)
        min_val_Pz = min(min(filtered_Pz_true), min(filtered_Pz_pred))
        max_val_Pz = max(max(filtered_Pz_true), max(filtered_Pz_pred))
        plt.plot([min_val_Pz, max_val_Pz], [min_val_Pz, max_val_Pz], 'r--', label='Ideal Line (y=x)')
        plt.xlabel('True Pz (kN)')
        plt.ylabel('Predicted Pz (kN)')
        plt.legend()
        plt.title(f"Pz: True vs Pred (Noise)")
        plt.grid(True)

        plt.subplot(133)
        if Yc_pred is not None:
            # 显示Yc预测值的分布
            plt.hist(Yc_pred, bins=20, alpha=0.7, label='Yc Prediction Distribution')
            plt.axvline(np.mean(Yc_pred), color='r', linestyle='--', label=f'Mean: {np.mean(Yc_pred):.1f} MPa')
            plt.xlabel('Yc (MPa)')
            plt.ylabel('Frequency')
            plt.legend()
            plt.title('Yc Prediction Distribution')
            plt.grid(True)
        plt.tight_layout()
        plt.savefig(f"results/9.png")
        plt.close()
    except:
        print(f"警告: 未找到 {filename} 的 Ours 模型结果，跳过图9")

# 表1：预测精度指标和计算时间统计
metrics_det = pd.DataFrame([{
    'Filename': r['filename'],
    'Model': r['model'],
    'MAE_S': r['mae_S'],
    'MSE_S': r['mse_S'],
    'MAE_Pz': r['mae_Pz'],
    'MSE_Pz': r['mse_Pz'],
    'Inference_Time': r['inference_time']
} for r in results_det])

metrics_rand = pd.DataFrame([{
    'Filename': r['filename'],
    'Model': r['model'],
    'MAE_S': r['mae_S'],
    'MSE_S': r['mse_S'],
    'MAE_Pz': r['mae_Pz'],
    'MSE_Pz': r['mse_Pz'],
    'Inference_Time': r['inference_time']
} for r in results_rand])

print("表1 预测精度指标和计算时间统计 - 确定情形:")
if not metrics_det.empty:
    print(metrics_det)
    metrics_det.to_csv("results/1_det.csv", index=False)
    print("\n确定情形平均值（按模型分组）:")
    print(metrics_det.groupby('Model')[['MAE_S', 'MSE_S', 'MAE_Pz', 'MSE_Pz',  'Inference_Time']].mean())
else:
    print("无确定情形下的预测结果")

print("\n表1 预测精度指标和计算时间统计 - 随机情形:")
if not metrics_rand.empty:
    print(metrics_rand)
    metrics_rand.to_csv("results/1_random.csv", index=False)
    print("\n随机情形平均值（按模型分组）:")
    print(metrics_rand.groupby('Model')[['MAE_S', 'MSE_S', 'MAE_Pz', 'MSE_Pz', 'Inference_Time']].mean())
else:
    print("无随机情形下的预测结果")


# 表2：确定情形算法比较预测精度
metrics_det_all = pd.DataFrame([{
    'Model': r['model'],
    'MAE_S': r['mae_S'],
    'MSE_S': r['mse_S'],
    'MAE_Pz': r['mae_Pz'],
    'MSE_Pz': r['mse_Pz']
} for r in results_det if r['model'] in ['Ours', 'LSTM', 'CNN', 'CNN-LSTM', 'Transformer']])
print("表2 确定情形 - 算法比较预测精度:")
if not metrics_det_all.empty:
    print(metrics_det_all.groupby('Model')[numeric_columns[:-1]].mean())
    metrics_det_all.to_csv("results/2.csv")
else:
    print("无 CNN 或 Transformer 的确定情形预测结果")

# 图11：确定情形按时间序列算法对比 (仅Ours模型演示)
for filename in test_filenames:
    try:
        result = next(r for r in results_det if r['filename'] == filename and r['model'] == 'Ours')
        t = result['t']
        plt.figure(figsize=(12, 10))

        # S 的时间序列对比
        plt.subplot(311)
        plt.plot(t, abs(result['S_pred']), 'b--', label='Ours Pred S')
        plt.plot(t, abs(result['S_true']), 'r-', label='True S')
        plt.xlabel('Time (s)')
        plt.ylabel('Stroke S (mm)')
        plt.legend()
        plt.title(f'Ours Model Prediction - Stroke S')

        # Pz 的时间序列对比
        plt.subplot(312)
        plt.plot(t, abs(result['Pz_pred']), 'b--', label='Ours Pred Pz')
        plt.plot(t, abs(result['Pz_true']), 'r-', label='True Pz')
        plt.xlabel('Time (s)')
        plt.ylabel('Load Pz (kN)')
        plt.legend()
        plt.title(f'Ours Model Prediction - Load Pz')

        # Yc 预测
        plt.subplot(313)
        if result.get('Yc_pred') is not None:
            plt.plot(t, result['Yc_pred'], 'g-', label='Ours Pred Yc')
            plt.axhline(np.mean(result['Yc_pred']), color='orange', linestyle='--',
                       label=f'Mean Yc: {np.mean(result["Yc_pred"]):.1f} MPa')
            plt.xlabel('Time (s)')
            plt.ylabel('Yc (MPa)')
            plt.legend()
            plt.title(f'Ours Model Prediction - Yc')

        plt.tight_layout()
        plt.savefig(f"results/11.png")
        plt.close()
        print(f"✓ 生成图11: Ours模型时间序列预测对比")
    except StopIteration:
        print(f"警告: 未找到 {filename} 的 Ours 模型结果，跳过图11")

# 图12：随机情形算法对比 (仅Ours模型演示)
for filename in test_filenames:
    result = load_and_process_exp_data(test_dir, filename, min_max_dict, add_noise=False)
    if result is None or len(result) != 7:
        print(f"测试文件 {filename} 加载失败，跳过图12")
        continue
    input_data_3d, input_data_2d, _, _, _, S_raw, Pz_raw = result
    input_data = input_data_3d  # 使用3D输入

    plt.figure(figsize=(12, 10))

    # 使用Ours模型进行预测
    model = models['Ours']().to(device)
    S_pred, Pz_pred, Yc_pred, _ = predict_model(model, input_data, 'Ours', min_max_dict)

    # S 的时间序列对比
    plt.subplot(311)
    plt.plot(input_data[:, 0], abs(S_pred), 'b--', label='Ours Pred S')
    plt.plot(input_data[:, 0], abs(S_raw), 'r-', label='True S')
    plt.xlabel('Time (s)')
    plt.ylabel('Stroke S (mm)')
    plt.legend()
    plt.title(f'Ours Model - Stroke S Prediction')

    # Pz 的时间序列对比
    plt.subplot(312)
    plt.plot(input_data[:, 0], Pz_pred, 'b--', label='Ours Pred Pz')
    plt.plot(input_data[:, 0], Pz_raw, 'r-', label='True Pz')
    plt.xlabel('Time (s)')
    plt.ylabel('Load Pz (kN)')
    plt.legend()
    plt.title(f'Ours Model - Load Pz Prediction')

    # Yc 预测
    plt.subplot(313)
    if Yc_pred is not None:
        plt.plot(input_data[:, 0], Yc_pred, 'g-', label='Ours Pred Yc')
        plt.axhline(np.mean(Yc_pred), color='orange', linestyle='--',
                   label=f'Mean Yc: {np.mean(Yc_pred):.1f} MPa')
        plt.xlabel('Time (s)')
        plt.ylabel('Yc (MPa)')
        plt.legend()
        plt.title(f'Ours Model - Yc Prediction')

    plt.tight_layout()
    plt.savefig(f"results/12.png")
    plt.close()
    print(f"✓ 生成图12: Ours模型随机情形预测")


# 图12_noise：随机情形算法对比 有噪声 (仅Ours模型演示)
for filename in test_filenames:
    result = load_and_process_exp_data(test_dir, filename, min_max_dict, add_noise=True)
    if result is None or len(result) != 7:
        print(f"测试文件 {filename} 加载失败，跳过图12_noise")
        continue
    input_data_3d, input_data_2d, _, _, _, S_raw, Pz_raw = result
    input_data = input_data_3d  # 使用3D输入

    plt.figure(figsize=(12, 10))

    # 使用Ours模型进行预测
    model = models['Ours']().to(device)
    S_pred, Pz_pred, Yc_pred, _ = predict_model(model, input_data, 'Ours', min_max_dict)

    # S 的时间序列对比
    plt.subplot(311)
    plt.plot(input_data[:, 0], abs(S_pred), 'b--', label='Ours Pred S')
    plt.plot(input_data[:, 0], abs(S_raw), 'r-', label='True S (Noise)')
    plt.xlabel('Time (s)')
    plt.ylabel('Stroke S (mm)')
    plt.legend()
    plt.title(f'Ours Model - Stroke S Prediction (with Noise)')

    # Pz 的时间序列对比
    plt.subplot(312)
    plt.plot(input_data[:, 0], Pz_pred, 'b--', label='Ours Pred Pz')
    plt.plot(input_data[:, 0], Pz_raw, 'r-', label='True Pz (Noise)')
    plt.xlabel('Time (s)')
    plt.ylabel('Load Pz (kN)')
    plt.legend()
    plt.title(f'Ours Model - Load Pz Prediction (with Noise)')

    # Yc 预测
    plt.subplot(313)
    if Yc_pred is not None:
        plt.plot(input_data[:, 0], Yc_pred, 'g-', label='Ours Pred Yc')
        plt.axhline(np.mean(Yc_pred), color='orange', linestyle='--',
                   label=f'Mean Yc: {np.mean(Yc_pred):.1f} MPa')
        plt.xlabel('Time (s)')
        plt.ylabel('Yc (MPa)')
        plt.legend()
        plt.title(f'Ours Model - Yc Prediction (with Noise)')

    plt.tight_layout()
    plt.savefig(f"results/12_noise.png")
    plt.close()
    print(f"✓ 生成图12_noise: Ours模型噪声情形预测")

