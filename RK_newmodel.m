function [datatestN]=RK_newmodel(tstep,t_lb,t_rb)
para=fun_parameter();
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%��ʼ��
tstep=0.001;
t_lb=0.0;
t_rb=3;
t=(t_lb:tstep:t_rb);
x1=zeros(length(t),1); %������λ��
x2=zeros(length(t),1); %�������ٶ�
x3=zeros(length(t),1); %������λ��
x4=zeros(length(t),1); %�������ٶ�
x5=zeros(length(t),1); %֧���г�=������λ��-������λ��
x6=zeros(length(t),1); %֧�������ٶ�
x7=zeros(length(t),1); %����λ��
x8=zeros(length(t),1); %�����ٶ�
x9=zeros(length(t),1); %�Ƕ�
x10=zeros(length(t),1); %���ٶ�
F_s=zeros(length(t),1);%֧��������
F_o=zeros(length(t),1);%��Һ������
Pz=zeros(length(t),1);%�����غ�
Px=zeros(length(t),1);%�����غ�
ns=zeros(length(t),1);
kk18=zeros(length(t),1);
S=zeros(length(t),1);
Yc=zeros(length(t),1);
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
yy5=polyval(para.p5,para.ss);%�Ϳ������ϣ����Ϳף�ʵ���ϲ���Ҫ���
yy6=polyval(para.p6,para.st);%�����̥��ֱ��
yy7=polyval(para.p7,2.03);%��ϲ�ͬ�߶��µĳ�ʼ�ٶ�

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%��ʼֵ
x1(1)=0;%������λ��
x2(1)=yy7;%�������ٶ�
x3(1)=0;%������λ��
x4(1)=yy7;%�������ٶ�
x5(1)=0;%֧���г��г�=������λ��-������λ��
x6(1)=0;%֧��ѹ������
x7(1)=0;%����λ��
x8(1)=3.92;%�����ٶ�
x9(1)=0;%�Ƕ�
x10(1)=0; %����
%ԭx10(1)=para.xx8/para.Rt; %����

F_s(1)=0; %��Һ������

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
L=0*(para.M_1+para.M_2)*para.g;          %����

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
for i=2:length(t)
    y1=x1(i-1);%������λ�ƣ���̥ѹ����
    y2=x2(i-1);%��̥ѹ�����ʣ�����
    y3=x3(i-1);%������λ��
    y4=x4(i-1);%������λ�Ƶ���
    y5=x5(i-1);%�г�
    y6=x6(i-1);%�г̵���
    y7=x7(i-1);%����λ��
    y8=x8(i-1);%�����ٶ�
    y9=x9(i-1);%�Ƕ�
    y10=x10(i-1);%���ٶ�
    Ah=polyval(para.p5,y5); %���Ϳ����
    %ԭAh=27.7e-6; %���Ϳ����

    Fa=para.Aal*(para.Pa0/(1-para.Aal*y5/para.Va0)^para.n-para.Patm);
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    if y6<0
        Fh=-para.r*para.Aa^3/(2*para.Cd1^2*Ah^2)*y6^2-para.r*para.Ahl^3/(2*para.Cds^2*para.Adl2^2)*y6^2;
    elseif y6>0
        Fh=para.r*para.Aa^3/(2*para.Cd2^2*Ah^2)*y6^2+para.r*para.Ahl^3/(2*para.Cds^2*para.Adl1^2)*y6^2;
    else
        Fh=0;
    end
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    N_s=para.K*y7+0.7*sqrt(para.M_2*para.K)*y8;
    N_u=(para.L_b-para.L_c-y5)/(para.L_c+y5)*N_s;%֧����֧��������
    N_l=para.L_b/(para.L_c+y5)*N_s;         %֧����֧��������
    %Ff=Km*Fa*sign(y6)+mu_b*(abs(N_u)+abs(N_l))*sign(y6);
    Ff=para.Km*Fa*sign(y6)+para.mu_b*abs(N_u+N_l)*sign(y6);
    if y5<=0   %��һ�׶��˶�����̥ѹ����֧����ѹ��
        x5(i-1)=0;
        x6(i-1)=0;
        Fs=0;
    else       %�ڶ��׶��˶�����̥ѹ����֧��ѹ��
        Fs=Fa+Fh+Ff;
    end
   delt=(abs(y1)+y1)/2.0;
   e=39.2-(para.Rt-delt/3)*y10-y8;
   %ԭ e=-60.5-(para.Rt-delt/3)*y10+y8;
   sg=abs(e/39.2);
    %ԭsg=abs(e/60.5);
    if sg<0.2
        mu_f=5.62*sg*para.mu_f0;
    elseif (sg<0.25)
        mu_f=5.62*0.2*para.mu_f0;
    elseif(sg<0.45)
        mu_f=(-0.62*sg+1.279)*para.mu_f0;
    else
        mu_f=para.mu_f0;
    end
 
    CT=0.04;
    Vt=(1+CT*y2)*polyval(para.p6,delt);
    k11=y2;            %deltaһ�׵���
    k12=(Fs-Vt)/para.M_2+para.g; %delta���׵���
    k13=y4;            %�������ٶ�
    k14=-(L+Fs)/para.M_1+para.g; %���������ٶ�
    k15=y4-y2;         %�г�S��һ�׵�
    k16=k14-k12;       %�г�S�Ķ��׵�
    if(e>0)
        k17=y8;            %�����ٶ�
        k18=mu_f*(Vt/para.M_2)+(N_u-N_l)/para.M_2; %������ٶ�
        %         k18=(N_u-N_l)/M_2; %������ٶ�
        k19=y10;            %���ٶ�
        k10=mu_f*(Vt/para.Iu)*(para.Rt-delt);  %�Ǽ��ٶ�
    else
        k17=y8;            %�����ٶ�
        %             k8=(N_u-N_l)/M_2; %������ٶ�
        k18=mu_f*(Vt/para.M_2)+(N_u-N_l)/para.M_2; %������ٶ�
        k19=y10;
        k10=0;
    end
    %%%%%%%%��������㷨
    y1=x1(i-1)+tstep/2*k11;
    y2=x2(i-1)+tstep/2*k12;
    y3=x3(i-1)+tstep/2*k13;
    y4=x4(i-1)+tstep/2*k14;
    y5=x5(i-1)+tstep/2*k15;
    y6=x6(i-1)+tstep/2*k16;
    y7=x7(i-1)+tstep/2*k17;
    y8=x8(i-1)+tstep/2*k18;
    y9=x9(i-1)+tstep/2*k19;
    y10=x10(i-1)+tstep/2*k10;
    
    Fa=para.Aal*(para.Pa0/(1-para.Aal*y5/para.Va0)^para.n-para.Patm);
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
   if y6<0
        Fh=-para.r*para.Aa^3/(2*para.Cd1^2*Ah^2)*y6^2-para.r*para.Ahl^3/(2*para.Cds^2*para.Adl2^2)*y6^2;
    elseif y6>0
        Fh=para.r*para.Aa^3/(2*para.Cd2^2*Ah^2)*y6^2+para.r*para.Ahl^3/(2*para.Cds^2*para.Adl1^2)*y6^2;
    else
        Fh=0;
    end
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    N_s=para.K*y7+0.7*sqrt(para.M_2*para.K)*y8;
    N_u=(para.L_b-para.L_c-y5)/(para.L_c+y5)*N_s;%֧����֧��������
    N_l=para.L_b/(para.L_c+y5)*N_s;         %֧����֧��������
    %Ff=Km*Fa*sign(y6)+mu_b*(abs(N_u)+abs(N_l))*sign(y6);
    Ff=para.Km*Fa*sign(y6)+para.mu_b*abs(N_u+N_l)*sign(y6);
    if y5<=0   %��һ�׶��˶�����̥ѹ����֧����ѹ��
        x5(i-1)=0;
        x6(i-1)=0;
        Fs=0;
    else       %�ڶ��׶��˶�����̥ѹ����֧��ѹ��
        Fs=Fa+Fh+Ff;
    end
   delt=(abs(y1)+y1)/2.0;
   e=39.2-(para.Rt-delt/3)*y10-y8;
   %ԭ e=-60.5-(para.Rt-delt/3)*y10+y8;
   sg=abs(e/39.2);
    %ԭsg=abs(e/60.5);
    if sg<0.2
        mu_f=5.62*sg*para.mu_f0;
    elseif (sg<0.25)
        mu_f=5.62*0.2*para.mu_f0;
    elseif(sg<0.45)
        mu_f=(-0.62*sg+1.279)*para.mu_f0;
    else
        mu_f=para.mu_f0;
    end
 
    CT=0.04;
    Vt=(1+CT*y2)*polyval(para.p6,delt);
    k21=y2;            %deltaһ�׵���
    k22=(Fs-Vt)/para.M_2+para.g; %delta���׵���
    k23=y4;            %�������ٶ�
    k24=-(L+Fs)/para.M_1+para.g; %���������ٶ�
    k25=y4-y2;         %�г�S��һ�׵�
    k26=k24-k22;       %�г�S�Ķ��׵�
    if(e>0)
        k27=y8;            %�����ٶ�
        k28=mu_f*(Vt/para.M_2)+(N_u-N_l)/para.M_2; %������ٶ�
        %         k18=(N_u-N_l)/M_2; %������ٶ�
        k29=y10;            %���ٶ�
        k20=mu_f*(Vt/para.Iu)*(para.Rt-delt);  %�Ǽ��ٶ�
    else
        k27=y8;            %�����ٶ�
        %             k8=(N_u-N_l)/M_2; %������ٶ�
        k28=mu_f*(Vt/para.M_2)+(N_u-N_l)/para.M_2; %������ٶ�
        k29=y10;
        k20=0;
    end
    %%%%%%%%��������㷨
    y1=x1(i-1)+tstep/2*k21;
    y2=x2(i-1)+tstep/2*k22;
    y3=x3(i-1)+tstep/2*k23;
    y4=x4(i-1)+tstep/2*k24;
    y5=x5(i-1)+tstep/2*k25;
    y6=x6(i-1)+tstep/2*k26;
    y7=x7(i-1)+tstep/2*k27;
    y8=x8(i-1)+tstep/2*k28;
    y9=x9(i-1)+tstep/2*k29;
    y10=x10(i-1)+tstep/2*k20;
    
    Fa=para.Aal*(para.Pa0/(1-para.Aal*y5/para.Va0)^para.n-para.Patm);
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    if y6<0
        Fh=-para.r*para.Aa^3/(2*para.Cd1^2*Ah^2)*y6^2-para.r*para.Ahl^3/(2*para.Cds^2*para.Adl2^2)*y6^2;
    elseif y6>0
        Fh=para.r*para.Aa^3/(2*para.Cd2^2*Ah^2)*y6^2+para.r*para.Ahl^3/(2*para.Cds^2*para.Adl1^2)*y6^2;
    else
        Fh=0;
    end
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    N_s=para.K*y7+0.7*sqrt(para.M_2*para.K)*y8;
    N_u=(para.L_b-para.L_c-y5)/(para.L_c+y5)*N_s;%֧����֧��������
    N_l=para.L_b/(para.L_c+y5)*N_s;         %֧����֧��������
    %Ff=Km*Fa*sign(y6)+mu_b*(abs(N_u)+abs(N_l))*sign(y6);
    Ff=para.Km*Fa*sign(y6)+para.mu_b*abs(N_u+N_l)*sign(y6);
    if y5<=0   %��һ�׶��˶�����̥ѹ����֧����ѹ��
        x5(i-1)=0;
        x6(i-1)=0;
        Fs=0;
    else       %�ڶ��׶��˶�����̥ѹ����֧��ѹ��
        Fs=Fa+Fh+Ff;
    end
   delt=(abs(y1)+y1)/2.0;
   e=39.2-(para.Rt-delt/3)*y10-y8;
   %ԭ e=-60.5-(para.Rt-delt/3)*y10+y8;
   sg=abs(e/39.2);
    %ԭsg=abs(e/60.5);
    if sg<0.2
        mu_f=5.62*sg*para.mu_f0;
    elseif (sg<0.25)
        mu_f=5.62*0.2*para.mu_f0;
    elseif(sg<0.45)
        mu_f=(-0.62*sg+1.279)*para.mu_f0;
    else
        mu_f=para.mu_f0;
    end
 
    CT=0.04;
    Vt=(1+CT*y2)*polyval(para.p6,delt);
    k31=y2;            %deltaһ�׵���
    k32=(Fs-Vt)/para.M_2+para.g; %delta���׵���
    k33=y4;            %�������ٶ�
    k34=-(L+Fs)/para.M_1+para.g; %���������ٶ�
    k35=y4-y2;         %�г�S��һ�׵�
    k36=k34-k32;       %�г�S�Ķ��׵�
    if(e>0)
        k37=y8;            %�����ٶ�
        k38=mu_f*(Vt/para.M_2)+(N_u-N_l)/para.M_2; %������ٶ�
        %         k18=(N_u-N_l)/M_2; %������ٶ�
        k39=y10;            %���ٶ�
        k30=mu_f*(Vt/para.Iu)*(para.Rt-delt);  %�Ǽ��ٶ�
    else
        k37=y8;            %�����ٶ�
        %             k8=(N_u-N_l)/M_2; %������ٶ�
        k38=mu_f*(Vt/para.M_2)+(N_u-N_l)/para.M_2; %������ٶ�
        k39=y10;
        k30=0;
    end
    %%%%%%%%��������㷨
    y1=x1(i-1)+tstep/2*k31;
    y2=x2(i-1)+tstep/2*k32;
    y3=x3(i-1)+tstep/2*k33;
    y4=x4(i-1)+tstep/2*k34;
    y5=x5(i-1)+tstep/2*k35;
    y6=x6(i-1)+tstep/2*k36;
    y7=x7(i-1)+tstep/2*k37;
    y8=x8(i-1)+tstep/2*k38;
    y9=x9(i-1)+tstep/2*k39;
    y10=x10(i-1)+tstep/2*k30;
    
    Fa=para.Aal*(para.Pa0/(1-para.Aal*y5/para.Va0)^para.n-para.Patm);
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
   if y6<0
        Fh=-para.r*para.Aa^3/(2*para.Cd1^2*Ah^2)*y6^2-para.r*para.Ahl^3/(2*para.Cds^2*para.Adl2^2)*y6^2;
    elseif y6>0
        Fh=para.r*para.Aa^3/(2*para.Cd2^2*Ah^2)*y6^2+para.r*para.Ahl^3/(2*para.Cds^2*para.Adl1^2)*y6^2;
    else
        Fh=0;
    end
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    N_s=para.K*y7+0.7*sqrt(para.M_2*para.K)*y8;
    N_u=(para.L_b-para.L_c-y5)/(para.L_c+y5)*N_s;%֧����֧��������
    N_l=para.L_b/(para.L_c+y5)*N_s;         %֧����֧��������
    %Ff=Km*Fa*sign(y6)+mu_b*(abs(N_u)+abs(N_l))*sign(y6);
    Ff=para.Km*Fa*sign(y6)+para.mu_b*abs(N_u+N_l)*sign(y6);
    if y5<=0   %��һ�׶��˶�����̥ѹ����֧����ѹ��
        x5(i-1)=0;
        x6(i-1)=0;
        Fs=0;
    else       %�ڶ��׶��˶�����̥ѹ����֧��ѹ��
        Fs=Fa+Fh+Ff;
    end
   delt=(abs(y1)+y1)/2.0;
   e=39.2-(para.Rt-delt/3)*y10-y8;
   %ԭ e=-60.5-(para.Rt-delt/3)*y10+y8;
   sg=abs(e/39.2);
    %ԭsg=abs(e/60.5);
    if sg<0.2
        mu_f=5.62*sg*para.mu_f0;
    elseif (sg<0.25)
        mu_f=5.62*0.2*para.mu_f0;
    elseif(sg<0.45)
        mu_f=(-0.62*sg+1.279)*para.mu_f0;
    else
        mu_f=para.mu_f0;
    end
 
    CT=0.04;
    Vt=(1+CT*y2)*polyval(para.p6,delt);
    k41=y2;            %deltaһ�׵���
    k42=(Fs-Vt)/para.M_2+para.g; %delta���׵���
    k43=y4;            %�������ٶ�
    k44=-(L+Fs)/para.M_1+para.g; %���������ٶ�
    k45=y4-y2;         %�г�S��һ�׵�
    k46=k44-k42;       %�г�S�Ķ��׵�
    if(e>0)
        k47=y8;            %�����ٶ�
        k48=-mu_f*(Vt/para.M_2)+(N_u-N_l)/para.M_2; %������ٶ�
        %         k18=(N_u-N_l)/M_2; %������ٶ�
        k49=y10;            %���ٶ�
        k40=mu_f*(Vt/para.Iu)*(para.Rt-delt);  %�Ǽ��ٶ�
    else
        k47=y8;            %�����ٶ�
        %             k8=(N_u-N_l)/M_2; %������ٶ�
        k48=mu_f*(Vt/para.M_2)+(N_u-N_l)/para.M_2; %������ٶ�
        k49=y10;
        k40=0;
    end
    %%%%%%%%��������㷨
    y1=x1(i-1)+tstep/2*k41;
    y2=x2(i-1)+tstep/2*k42;
    y3=x3(i-1)+tstep/2*k43;
    y4=x4(i-1)+tstep/2*k44;
    y5=x5(i-1)+tstep/2*k45;
    y6=x6(i-1)+tstep/2*k46;
    y7=x7(i-1)+tstep/2*k47;
    y8=x8(i-1)+tstep/2*k48;
    y9=x9(i-1)+tstep/2*k49;
    y10=x10(i-1)+tstep/2*k40;
    x1(i)=x1(i-1)+tstep/6*(k11+2*k21+2*k31+k41);   %һ��ѭ����õ�����̥ѹ����
    x2(i)=x2(i-1)+tstep/6*(k12+2*k22+2*k32+k42);   %һ��ѭ����õ�����̥ѹ������
    x3(i)=x3(i-1)+tstep/6*(k13+2*k23+2*k33+k43);   %һ��ѭ����õ���������λ��
    x4(i)=x4(i-1)+tstep/6*(k14+2*k24+2*k34+k44);   %һ��ѭ����õ����������ٶ�
    x5(i)=x5(i-1)+tstep/6*(k15+2*k25+2*k35+k45);   %һ��ѭ����õ����г�
    x6(i)=x6(i-1)+tstep/6*(k16+2*k26+2*k36+k46);   %һ��ѭ����õ����г�ѹ������
    x7(i)=x7(i-1)+tstep/6*(k17+2*k27+2*k37+k47);   %һ��ѭ����õ��ĺ���λ��
    x8(i)=x8(i-1)+tstep/6*(k18+2*k28+2*k38+k48);   %һ��ѭ����õ��ĺ����ٶ�
    x9(i)=x9(i-1)+tstep/6*(k19+2*k29+2*k39+k49);   %һ��ѭ����õ��ĽǶ�
    x10(i)=x10(i-1)+tstep/6*(k10+2*k20+2*k30+k40); %һ��ѭ����õ��Ľ��ٶ�
    %���ݶ��������Ӧ�����
    F_s(i) = Fs;
    Pz(i)=3.09e5*x1(i);%�������
    Px(i)=mu_f*Pz(i)+N_s;%�������
    ns(i)=N_s;
    kk18(i)=mu_f;
    Yc(i)=x5(i)+x1(i);%����λ��
    S(i)=x5(i);%֧��λ��

end
datatestN.T=t;
datatestN.S=S;%%%%%��ע�����1
datatestN.Yc=Yc;
datatestN.Pz=Pz;%%%%%��ע�����2
datatestN.Px=Px;
% plot(datatestB2.T,datatestB2.S,'r')


end

