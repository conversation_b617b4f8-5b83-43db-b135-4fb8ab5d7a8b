#!/usr/bin/env python3
"""
数据特征分析脚本 - 专门用于提取和分析数据特征
"""

import numpy as np
import pandas as pd
import os
import glob
import re

def extract_info_from_filename(filename):
    """从文件名提取质量和高度信息"""
    print(f"分析文件名: {filename}")
    
    # 尝试不同的模式匹配
    patterns = [
        r'(\d+)kg.*?(\d+)mm',  # 质量kg_高度mm
        r'(\d+)mm.*?(\d+)kg',  # 高度mm_质量kg
        r'M(\d+).*?H(\d+)',    # M质量_H高度
        r'H(\d+).*?M(\d+)',    # H高度_M质量
        r'(\d+)_(\d+)',        # 数字_数字
    ]
    
    for pattern in patterns:
        match = re.search(pattern, filename)
        if match:
            num1, num2 = match.groups()
            num1, num2 = float(num1), float(num2)
            
            # 判断哪个是质量，哪个是高度
            if 'kg' in filename and 'mm' in filename:
                if 'kg' in match.group(0):
                    mass, height = num1, num2
                else:
                    height, mass = num1, num2
            elif num1 > 100 and num2 > 100:  # 都是大数字
                if num1 > num2:
                    height, mass = num1, num2  # 假设较大的是高度
                else:
                    mass, height = num1, num2  # 假设较大的是高度
            else:
                mass, height = num1, num2
            
            print(f"  提取结果: 质量={mass}kg, 高度={height}mm")
            return height, mass
    
    print(f"  无法提取信息，使用默认值")
    return 1500.0, 1500.0

def analyze_data_file(file_path):
    """分析单个数据文件"""
    filename = os.path.basename(file_path)
    print(f"\n📊 分析文件: {filename}")
    
    height, mass = extract_info_from_filename(filename)
    
    # 尝试读取文件
    data_loaded = False
    df = None
    
    for encoding in ['utf-8', 'gbk', 'latin-1', 'cp1252']:
        for sep in ['\t', ',', ' ', ';']:
            for skip_rows in range(10, 20):
                try:
                    df = pd.read_csv(file_path, sep=sep, encoding=encoding, 
                                   skiprows=skip_rows, header=None)
                    
                    if df.shape[1] >= 6 and len(df) > 50:
                        print(f"  ✅ 成功读取: 形状={df.shape}, 编码={encoding}, 分隔符='{sep}', 跳过行数={skip_rows}")
                        data_loaded = True
                        break
                except:
                    continue
            if data_loaded:
                break
        if data_loaded:
            break
    
    if not data_loaded:
        print(f"  ❌ 无法读取文件")
        return None
    
    # 分析数据内容
    print(f"  📈 数据分析:")
    print(f"    行数: {len(df)}")
    print(f"    列数: {df.shape[1]}")
    
    # 显示前几行
    print(f"    前5行数据:")
    print(df.head().to_string())
    
    # 尝试识别列
    if df.shape[1] >= 6:
        # 假设列的含义 (根据FINAL文件中的处理)
        time_col = df.iloc[:, 0]  # 时间
        col1 = df.iloc[:, 1]      # 可能是某个传感器
        col2 = df.iloc[:, 2]      # 可能是某个传感器
        Pz = df.iloc[:, 3]        # Pz (垂直载荷)
        col4 = df.iloc[:, 4]      # 可能是某个传感器
        S = df.iloc[:, 5]         # S (支柱行程)
        
        print(f"    数据范围分析:")
        print(f"      时间列: [{time_col.min():.3f}, {time_col.max():.3f}]")
        print(f"      Pz列: [{Pz.min():.3f}, {Pz.max():.3f}]")
        print(f"      S列: [{S.min():.3f}, {S.max():.3f}]")
        
        # 计算Yc (根据FINAL文件的逻辑)
        Yc = S + Pz * 0.01  # 或者其他计算方式
        print(f"      Yc计算: [{Yc.min():.3f}, {Yc.max():.3f}]")
        
        # 检查数据有效性
        valid_data = not (np.any(np.isnan(S)) or np.any(np.isnan(Pz)) or np.any(np.isnan(Yc)))
        print(f"    数据有效性: {'✅ 有效' if valid_data else '❌ 包含NaN'}")
        
        if valid_data:
            return {
                'filename': filename,
                'mass': mass,
                'height': height,
                'time': time_col.values,
                'Pz': Pz.values,
                'S': S.values,
                'Yc': Yc.values,
                'data_shape': df.shape
            }
    
    return None

def analyze_all_data():
    """分析所有数据文件"""
    print("🚀 开始分析所有数据文件...")
    print("="*60)
    
    # 数据目录
    data_dirs = {
        'Train': 'data/Data_Train',
        'Test': 'data/Data_Test', 
        'Transfer': 'data/Data_Transfer'
    }
    
    all_results = {}
    
    for dataset_name, data_dir in data_dirs.items():
        print(f"\n📁 分析 {dataset_name} 数据集...")
        
        if not os.path.exists(data_dir):
            print(f"  ❌ 目录不存在: {data_dir}")
            continue
        
        files = glob.glob(os.path.join(data_dir, "*.txt"))
        print(f"  找到 {len(files)} 个文件")
        
        dataset_results = []
        
        for i, file_path in enumerate(files[:10]):  # 只分析前10个文件
            print(f"\n  处理文件 {i+1}/{min(10, len(files))}")
            result = analyze_data_file(file_path)
            
            if result:
                dataset_results.append(result)
        
        all_results[dataset_name] = dataset_results
        print(f"\n  {dataset_name} 数据集总结:")
        print(f"    成功处理: {len(dataset_results)} 个文件")
        
        if dataset_results:
            # 统计质量高度组合
            mass_height_combinations = set()
            for result in dataset_results:
                mass_height_combinations.add((result['mass'], result['height']))
            
            print(f"    质量高度组合:")
            for mass, height in sorted(mass_height_combinations):
                print(f"      质量={mass:.0f}kg, 高度={height:.0f}mm")
    
    return all_results

def generate_feature_summary(all_results):
    """生成特征总结"""
    print("\n" + "="*60)
    print("📋 数据特征总结")
    print("="*60)
    
    for dataset_name, results in all_results.items():
        if not results:
            continue
            
        print(f"\n📊 {dataset_name} 数据集特征:")
        
        # 收集所有数据
        all_masses = [r['mass'] for r in results]
        all_heights = [r['height'] for r in results]
        all_pz = np.concatenate([r['Pz'] for r in results])
        all_s = np.concatenate([r['S'] for r in results])
        all_yc = np.concatenate([r['Yc'] for r in results])
        
        print(f"  样本数量: {len(results)}")
        print(f"  质量范围: [{min(all_masses):.0f}, {max(all_masses):.0f}] kg")
        print(f"  高度范围: [{min(all_heights):.0f}, {max(all_heights):.0f}] mm")
        print(f"  Pz范围: [{all_pz.min():.3f}, {all_pz.max():.3f}]")
        print(f"  S范围: [{all_s.min():.3f}, {all_s.max():.3f}]")
        print(f"  Yc范围: [{all_yc.min():.3f}, {all_yc.max():.3f}]")
        
        # 统计不同的质量高度组合
        unique_combinations = set()
        for result in results:
            unique_combinations.add((result['mass'], result['height']))
        
        print(f"  不同质量高度组合: {len(unique_combinations)} 种")
        print(f"  组合详情:")
        for mass, height in sorted(unique_combinations):
            count = sum(1 for r in results if r['mass'] == mass and r['height'] == height)
            print(f"    M={mass:.0f}kg, H={height:.0f}mm: {count} 个样本")

def create_data_extraction_code():
    """生成数据提取代码"""
    print("\n" + "="*60)
    print("💻 生成数据提取代码")
    print("="*60)
    
    code = '''
# 数据提取和特征工程代码
import numpy as np
import pandas as pd
import torch

def extract_features_from_file(file_path):
    """从文件提取特征"""
    # 1. 从文件名提取质量和高度
    filename = os.path.basename(file_path)
    height, mass = extract_info_from_filename(filename)
    
    # 2. 读取数据文件
    df = pd.read_csv(file_path, sep='\\t', encoding='utf-8', skiprows=14, header=None)
    
    # 3. 提取关键变量
    time = df.iloc[:, 0].values
    Pz = df.iloc[:, 3].values  # 垂直载荷
    S = df.iloc[:, 5].values   # 支柱行程
    Yc = S + Pz * 0.01         # 吊篮位移 (根据实际公式调整)
    
    # 4. 特征工程
    # 基础特征
    mass_norm = mass / 3000.0  # 归一化质量
    height_norm = height / 3000.0  # 归一化高度
    
    # 导数特征
    dS_dt = np.gradient(S)  # S的一阶导数
    d2S_dt2 = np.gradient(dS_dt)  # S的二阶导数
    dPz_dt = np.gradient(Pz)  # Pz的一阶导数
    d2Pz_dt2 = np.gradient(dPz_dt)  # Pz的二阶导数
    
    # 物理交互特征
    mass_height_interaction = mass_norm * height_norm
    mass_height_ratio = mass_norm / (height_norm + 1e-8)
    sqrt_mass_height = np.sqrt(mass_norm * height_norm + 1e-8)
    
    return {
        'mass': mass,
        'height': height,
        'mass_norm': mass_norm,
        'height_norm': height_norm,
        'time': time,
        'Pz': Pz,
        'S': S,
        'Yc': Yc,
        'dS_dt': dS_dt,
        'd2S_dt2': d2S_dt2,
        'dPz_dt': dPz_dt,
        'd2Pz_dt2': d2Pz_dt2,
        'mass_height_interaction': mass_height_interaction,
        'mass_height_ratio': mass_height_ratio,
        'sqrt_mass_height': sqrt_mass_height
    }

def create_training_features(data_list):
    """创建训练特征"""
    # 对于训练阶段：使用全部传感器数据
    features = []
    targets = []
    
    for data in data_list:
        # 输入特征 (20维)
        feature_vector = np.array([
            data['mass_norm'],
            data['height_norm'],
            data['mass_height_interaction'],
            data['mass_height_ratio'],
            data['sqrt_mass_height'],
            # 可以添加更多传感器特征...
        ])
        
        # 目标变量 (3维)
        target_vector = np.array([
            data['S'],
            data['Pz'], 
            data['Yc']
        ])
        
        features.append(feature_vector)
        targets.append(target_vector)
    
    return np.array(features), np.array(targets)

def create_transfer_features(data_list):
    """创建迁移学习特征 (仅质量和高度)"""
    features = []
    targets = []
    
    for data in data_list:
        # 输入特征 (2维) - 仅质量和高度
        feature_vector = np.array([
            data['mass_norm'],
            data['height_norm']
        ])
        
        # 目标变量 (3维)
        target_vector = np.array([
            data['S'],
            data['Pz'],
            data['Yc']
        ])
        
        features.append(feature_vector)
        targets.append(target_vector)
    
    return np.array(features), np.array(targets)
'''
    
    print("生成的特征提取代码:")
    print(code)
    
    # 保存代码到文件
    with open('feature_extraction_code.py', 'w', encoding='utf-8') as f:
        f.write(code)
    
    print("✅ 代码已保存到 feature_extraction_code.py")

def main():
    """主函数"""
    print("🚀 开始数据特征分析...")
    
    # 1. 分析所有数据
    all_results = analyze_all_data()
    
    # 2. 生成特征总结
    generate_feature_summary(all_results)
    
    # 3. 生成数据提取代码
    create_data_extraction_code()
    
    print("\n🎉 数据特征分析完成！")

if __name__ == "__main__":
    main()
