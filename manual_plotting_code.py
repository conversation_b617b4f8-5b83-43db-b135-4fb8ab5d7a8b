
# 手动运行的matplotlib代码
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 1. 响应曲线图
def plot_response_curves():
    df = pd.read_csv('response_curves_data.csv')
    
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('Different Mass-Height Combinations Response Curves', fontsize=16, fontweight='bold')
    
    variables = ['Pz', 'S', 'Yc']
    units = ['(kN)', '(mm)', '(mm)']
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F']
    
    combinations = df['combination'].unique()
    
    for var_idx, (var, unit) in enumerate(zip(variables, units)):
        ax = axes[var_idx]
        
        for i, combo in enumerate(combinations):
            combo_data = df[df['combination'] == combo]
            mass = combo_data['mass'].iloc[0]
            height = combo_data['height'].iloc[0]
            
            ax.plot(combo_data['time'], combo_data[var], 
                   color=colors[i % len(colors)], linewidth=2.5, alpha=0.8,
                   label=f"M={mass}kg, H={height}mm")
        
        ax.set_xlabel('Normalized Time', fontsize=12)
        ax.set_ylabel(f'{var} {unit}', fontsize=12)
        ax.set_title(f'{var} Response Curves', fontsize=13, fontweight='bold')
        ax.grid(True, alpha=0.3)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=9)
    
    plt.tight_layout()
    plt.savefig('figure1_response_curves.png', dpi=300, bbox_inches='tight')
    plt.show()

# 2. 方法对比图
def plot_method_comparison():
    df = pd.read_csv('method_performance_data.csv')
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Method Performance Comparison (EnhancedFinalBest Best)', fontsize=16, fontweight='bold')
    
    # 整体性能对比
    ax1 = axes[0, 0]
    bars = ax1.bar(df['methods'], df['overall_mape'], 
                   color=['gold', 'silver', '#CD7F32', 'gray'], alpha=0.8)
    ax1.set_ylabel('Average MAPE (%)')
    ax1.set_title('Overall Performance Comparison')
    ax1.grid(True, alpha=0.3, axis='y')
    
    for bar, value in zip(bars, df['overall_mape']):
        ax1.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.3,
                f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('figure7_method_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

# 运行函数
plot_response_curves()
plot_method_comparison()
