#!/usr/bin/env python3
"""
Step 5: 精度影响分析
"""

import torch
import torch.nn as nn
import torch.optim as optim
from modelgai import StreamlinedTransferPredictor, EnhancedLandingGearPhysicsModule

def create_realistic_data(batch_size=8, seq_len=50):
    """创建更真实的测试数据"""
    # 基于实际数据范围创建测试数据
    
    # 输入数据 (质量, 高度) - 归一化到[0,1]
    mass_actual = torch.rand(batch_size, seq_len) * 2500 + 500  # 500-3000 kg
    height_actual = torch.rand(batch_size, seq_len) * 2500 + 500  # 500-3000 mm
    
    mass_norm = mass_actual / 3000.0  # 归一化
    height_norm = height_actual / 3000.0
    
    inputs = torch.stack([mass_norm, height_norm], dim=-1)
    
    # 目标数据 - 基于实际数据范围
    # S: [-536.62, 45.98] mm -> 归一化
    s_min, s_max = -536.617130, 45.976616
    S_actual = torch.rand(batch_size, seq_len) * (s_max - s_min) + s_min
    S_norm = (S_actual - s_min) / (s_max - s_min)
    
    # Pz: [-1.63, 203.92] kN -> 归一化
    pz_min, pz_max = -1.630496, 203.920620
    Pz_actual = torch.rand(batch_size, seq_len) * (pz_max - pz_min) + pz_min
    Pz_norm = (Pz_actual - pz_min) / (pz_max - pz_min)
    
    # Yc: [0, 100] mm -> 归一化
    yc_max = 100
    Yc_actual = torch.rand(batch_size, seq_len) * yc_max
    Yc_norm = Yc_actual / yc_max
    
    targets = torch.stack([S_norm, Pz_norm, Yc_norm], dim=-1)
    
    return inputs, targets, {
        'mass_actual': mass_actual,
        'height_actual': height_actual,
        'S_actual': S_actual,
        'Pz_actual': Pz_actual,
        'Yc_actual': Yc_actual
    }

def test_physics_constraint_impact():
    """测试物理约束对精度的影响"""
    print("="*60)
    print("🔍 Step 5: 物理约束精度影响分析")
    print("="*60)
    
    # 创建两个模型：有物理约束和无物理约束
    model_with_physics = StreamlinedTransferPredictor(
        input_dim=2, hidden_dim=128, seq_len=50, use_physics=True
    )
    
    model_without_physics = StreamlinedTransferPredictor(
        input_dim=2, hidden_dim=128, seq_len=50, use_physics=False
    )
    
    # 创建真实数据
    inputs, targets, actual_data = create_realistic_data()
    
    print(f"📊 测试数据统计:")
    print(f"  批次大小: {inputs.shape[0]}")
    print(f"  序列长度: {inputs.shape[1]}")
    print(f"  质量范围: [{actual_data['mass_actual'].min():.0f}, {actual_data['mass_actual'].max():.0f}] kg")
    print(f"  高度范围: [{actual_data['height_actual'].min():.0f}, {actual_data['height_actual'].max():.0f}] mm")
    print(f"  S范围: [{actual_data['S_actual'].min():.1f}, {actual_data['S_actual'].max():.1f}] mm")
    print(f"  Pz范围: [{actual_data['Pz_actual'].min():.1f}, {actual_data['Pz_actual'].max():.1f}] kN")
    print(f"  Yc范围: [{actual_data['Yc_actual'].min():.1f}, {actual_data['Yc_actual'].max():.1f}] mm")
    
    results = {}
    
    for model_name, model in [("有物理约束", model_with_physics), ("无物理约束", model_without_physics)]:
        print(f"\n🔍 测试模型: {model_name}")
        
        model.eval()
        with torch.no_grad():
            outputs = model(inputs, epoch_ratio=0.5)
            predictions = outputs['predictions']
            
            # 计算各变量的MAPE
            def calculate_mape(pred, target):
                return torch.mean(torch.abs((pred - target) / (target + 1e-8))) * 100
            
            s_mape = calculate_mape(predictions[:, :, 0], targets[:, :, 0])
            pz_mape = calculate_mape(predictions[:, :, 1], targets[:, :, 1])
            yc_mape = calculate_mape(predictions[:, :, 2], targets[:, :, 2])
            avg_mape = (s_mape + pz_mape + yc_mape) / 3
            
            results[model_name] = {
                'S_MAPE': s_mape.item(),
                'Pz_MAPE': pz_mape.item(),
                'Yc_MAPE': yc_mape.item(),
                'Avg_MAPE': avg_mape.item()
            }
            
            print(f"  S变量 MAPE: {s_mape.item():.2f}%")
            print(f"  Pz变量 MAPE: {pz_mape.item():.2f}%")
            print(f"  Yc变量 MAPE: {yc_mape.item():.2f}%")
            print(f"  平均 MAPE: {avg_mape.item():.2f}%")
            
            if model_name == "有物理约束":
                print(f"  物理权重: {outputs.get('physics_weight', 'N/A'):.4f}")
    
    # 比较结果
    print(f"\n📊 物理约束影响分析:")
    with_physics = results["有物理约束"]
    without_physics = results["无物理约束"]
    
    for var in ['S_MAPE', 'Pz_MAPE', 'Yc_MAPE', 'Avg_MAPE']:
        diff = with_physics[var] - without_physics[var]
        improvement = "改善" if diff < 0 else "恶化"
        print(f"  {var}: {improvement} {abs(diff):.2f}%")
    
    return results

def test_feature_engineering_impact():
    """测试特征工程对精度的影响"""
    print("\n" + "="*60)
    print("🔍 特征工程精度影响分析")
    print("="*60)
    
    # 创建测试数据
    inputs, targets, actual_data = create_realistic_data()
    
    # 测试不同的特征工程配置
    configs = [
        {"name": "基础特征", "hidden_dim": 64},
        {"name": "增强特征", "hidden_dim": 128},
        {"name": "大容量特征", "hidden_dim": 256}
    ]
    
    results = {}
    
    for config in configs:
        print(f"\n🔍 测试配置: {config['name']}")
        
        model = StreamlinedTransferPredictor(
            input_dim=2,
            hidden_dim=config['hidden_dim'],
            seq_len=50,
            use_physics=True
        )
        
        model.eval()
        with torch.no_grad():
            outputs = model(inputs, epoch_ratio=0.5)
            predictions = outputs['predictions']
            
            # 计算MAPE
            s_mape = torch.mean(torch.abs((predictions[:, :, 0] - targets[:, :, 0]) / (targets[:, :, 0] + 1e-8))) * 100
            pz_mape = torch.mean(torch.abs((predictions[:, :, 1] - targets[:, :, 1]) / (targets[:, :, 1] + 1e-8))) * 100
            yc_mape = torch.mean(torch.abs((predictions[:, :, 2] - targets[:, :, 2]) / (targets[:, :, 2] + 1e-8))) * 100
            avg_mape = (s_mape + pz_mape + yc_mape) / 3
            
            results[config['name']] = {
                'S_MAPE': s_mape.item(),
                'Pz_MAPE': pz_mape.item(),
                'Yc_MAPE': yc_mape.item(),
                'Avg_MAPE': avg_mape.item(),
                'Parameters': sum(p.numel() for p in model.parameters())
            }
            
            print(f"  参数数量: {results[config['name']]['Parameters']:,}")
            print(f"  平均 MAPE: {avg_mape.item():.2f}%")
    
    return results

def test_loss_function_impact():
    """测试损失函数设计对精度的影响"""
    print("\n" + "="*60)
    print("🔍 损失函数精度影响分析")
    print("="*60)
    
    model = StreamlinedTransferPredictor(
        input_dim=2, hidden_dim=128, seq_len=50, use_physics=True
    )
    
    inputs, targets, actual_data = create_realistic_data()
    
    # 测试不同的损失权重配置
    weight_configs = [
        {"name": "均衡权重", "S": 1.0, "Pz": 1.0, "Yc": 1.0},
        {"name": "重点S", "S": 5.0, "Pz": 3.0, "Yc": 2.0},
        {"name": "重点Pz", "S": 2.0, "Pz": 5.0, "Yc": 2.0},
        {"name": "重点Yc", "S": 2.0, "Pz": 2.0, "Yc": 5.0}
    ]
    
    results = {}
    
    for config in weight_configs:
        print(f"\n🔍 测试权重配置: {config['name']}")
        
        # 临时修改损失权重
        original_weights = model.loss_weights.copy()
        model.loss_weights['S'] = config['S']
        model.loss_weights['Pz'] = config['Pz']
        model.loss_weights['Yc'] = config['Yc']
        
        model.train()
        outputs = model(inputs, epoch_ratio=0.5)
        loss, loss_dict = model.compute_multitask_loss(outputs, targets, epoch_ratio=0.5)
        
        print(f"  总损失: {loss.item():.6f}")
        print(f"  S损失: {loss_dict['loss_S']:.6f}")
        print(f"  Pz损失: {loss_dict['loss_Pz']:.6f}")
        print(f"  Yc损失: {loss_dict['loss_Yc']:.6f}")
        print(f"  物理损失: {loss_dict['loss_physics']:.6f}")
        
        results[config['name']] = {
            'total_loss': loss.item(),
            'S_loss': loss_dict['loss_S'],
            'Pz_loss': loss_dict['loss_Pz'],
            'Yc_loss': loss_dict['loss_Yc'],
            'physics_loss': loss_dict['loss_physics']
        }
        
        # 恢复原始权重
        model.loss_weights = original_weights
    
    return results

def analyze_rk_physics_completeness():
    """分析RK物理模型的完整性"""
    print("\n" + "="*60)
    print("🔍 RK物理模型完整性分析")
    print("="*60)
    
    physics_module = EnhancedLandingGearPhysicsModule()
    
    print(f"📊 物理参数完整性检查:")
    
    # 检查关键参数是否与MATLAB文件一致
    expected_params = {
        'Aa': 1385e-6,      # 主腔压缩面积
        'Aal': 2122e-6,     # 副腔压缩面积  
        'Pa0': 1.65e6,      # 初始压力
        'Va0': 461e-6,      # 初始体积
        'K': 3e3,           # 轮胎支撑刚度系数
        'M_1': 661.0,       # 落锤质量
        'M_2': 24.0,        # 机轮质量
        'r': 861.0,         # 油液密度
    }
    
    for param_name, expected_value in expected_params.items():
        if hasattr(physics_module, param_name):
            actual_value = getattr(physics_module, param_name).item()
            diff_percent = abs(actual_value - expected_value) / expected_value * 100
            
            if diff_percent < 1.0:
                status = "✅"
            elif diff_percent < 5.0:
                status = "⚠️"
            else:
                status = "❌"
            
            print(f"  {param_name}: {status} 期望={expected_value}, 实际={actual_value:.6f}, 差异={diff_percent:.2f}%")
        else:
            print(f"  {param_name}: ❌ 参数缺失")
    
    # 测试物理计算的合理性
    print(f"\n📊 物理计算合理性测试:")
    
    test_heights = torch.tensor([1000.0, 2000.0, 3000.0])  # mm
    test_masses = torch.tensor([500.0, 1000.0, 1500.0])    # kg
    
    Pz, S, Yc = physics_module(test_heights, test_masses)
    
    print(f"  输入高度: {test_heights.tolist()} mm")
    print(f"  输入质量: {test_masses.tolist()} kg")
    print(f"  输出Pz: {Pz.tolist()} kN")
    print(f"  输出S: {S.tolist()} mm")
    print(f"  输出Yc: {Yc.tolist()} mm")
    
    # 检查输出合理性
    reasonable = True
    
    if not (0 <= Pz.min() and Pz.max() <= 300):  # Pz应该在合理范围内
        print(f"  ❌ Pz超出合理范围 [0, 300] kN")
        reasonable = False
    
    if not (-600 <= S.min() and S.max() <= 100):  # S应该在合理范围内
        print(f"  ❌ S超出合理范围 [-600, 100] mm")
        reasonable = False
    
    if not (0 <= Yc.min() and Yc.max() <= 150):  # Yc应该在合理范围内
        print(f"  ❌ Yc超出合理范围 [0, 150] mm")
        reasonable = False
    
    if reasonable:
        print(f"  ✅ 物理计算输出在合理范围内")
    
    return reasonable

def main():
    print("🚀 开始Step 5: 精度影响分析")
    
    # 1. 物理约束影响分析
    physics_results = test_physics_constraint_impact()
    
    # 2. 特征工程影响分析
    feature_results = test_feature_engineering_impact()
    
    # 3. 损失函数影响分析
    loss_results = test_loss_function_impact()
    
    # 4. RK物理模型完整性分析
    physics_complete = analyze_rk_physics_completeness()
    
    print("\n" + "="*60)
    print("📋 Step 5 精度影响分析总结:")
    print("="*60)
    
    print(f"✅ 物理约束影响: 已分析")
    print(f"✅ 特征工程影响: 已分析")
    print(f"✅ 损失函数影响: 已分析")
    print(f"{'✅' if physics_complete else '❌'} RK物理模型: {'完整' if physics_complete else '需要改进'}")
    
    print(f"\n🎯 Step 5 完成，可以进行 Step 6: 整合测试")
    
    return True

if __name__ == "__main__":
    main()
